--cpu Cortex-M3
".\output\main.o"
".\output\stm32f10x_it.o"
".\output\bsp_led.o"
".\output\bsp_key.o"
".\output\bsp_usart.o"
".\output\startup_stm32f10x_hd.o"
".\output\core_cm3.o"
".\output\system_stm32f10x.o"
".\output\misc.o"
".\output\stm32f10x_adc.o"
".\output\stm32f10x_bkp.o"
".\output\stm32f10x_can.o"
".\output\stm32f10x_cec.o"
".\output\stm32f10x_crc.o"
".\output\stm32f10x_dac.o"
".\output\stm32f10x_dma.o"
".\output\stm32f10x_exti.o"
".\output\stm32f10x_flash.o"
".\output\stm32f10x_fsmc.o"
".\output\stm32f10x_gpio.o"
".\output\stm32f10x_i2c.o"
".\output\stm32f10x_iwdg.o"
".\output\stm32f10x_pwr.o"
".\output\stm32f10x_rcc.o"
".\output\stm32f10x_rtc.o"
".\output\stm32f10x_sdio.o"
".\output\stm32f10x_spi.o"
".\output\stm32f10x_tim.o"
".\output\stm32f10x_usart.o"
".\output\stm32f10x_wwdg.o"
--library_type=microlib --strict --scatter ".\Output\STM32F103.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list ".\Output\STM32F103.map" -o .\Output\STM32F103.axf