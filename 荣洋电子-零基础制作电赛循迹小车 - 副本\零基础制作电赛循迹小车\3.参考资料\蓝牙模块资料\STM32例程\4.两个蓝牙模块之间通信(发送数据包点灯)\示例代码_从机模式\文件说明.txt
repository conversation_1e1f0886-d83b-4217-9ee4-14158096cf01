
如何进入工程：  打开文件夹Project, 双击里面那个绿色图标文件，即可进入Keil编辑此工程。


【BSP】
由用户编写、添加的驱动文件存放位置，如LED、LCD等文件。


【Core】
核心相关，如启动文件、寄存器宏定义文件等;
不用管。不能删除。


【Project】
工程文件夹。里面有重要的工程入口文件 ：STM32F103.uvprojx（绿色的)
平时编译工程，双击此文件即可进入。


【STM32F10x_StdPeriph_Driver】
存放了官方库函数文件，即标准库文件夹。
不用管。不能删除。


【System】
存放了任务轮询器、系统功能文件; 
辅助性，非必要。


【User】
存放了三个主要文件：main.c、stm32f10x_it.c、stm32f4xx_it.c.h、stm32f10x_conf.h。
main.c：程序入口文件，里面写了main( )函数;
stm32f10x_it.c：中断服务函数文件; 提示：中断函数并非必须写在这里。
stm3210x_conf.h：库文件引用配置；通过注释、取消注释来引用库文件。

