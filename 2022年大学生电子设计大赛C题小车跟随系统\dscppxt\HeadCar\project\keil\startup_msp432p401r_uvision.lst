


ARM Macro Assembler    Page 1 


    1 00000000         ;//*****************************************************
                       ************************
    2 00000000         ;//
    3 00000000         ;// Copyright (C) 2012 - 2018 Texas Instruments Incorpor
                       ated - http://www.ti.com/
    4 00000000         ;//
    5 00000000         ;// Redistribution and use in source and binary forms, w
                       ith or without
    6 00000000         ;// modification, are permitted provided that the follow
                       ing conditions
    7 00000000         ;// are met:
    8 00000000         ;//
    9 00000000         ;//  Redistributions of source code must retain the abov
                       e copyright
   10 00000000         ;//  notice, this list of conditions and the following d
                       isclaimer.
   11 00000000         ;//
   12 00000000         ;//  Redistributions in binary form must reproduce the a
                       bove copyright
   13 00000000         ;//  notice, this list of conditions and the following d
                       isclaimer in the
   14 00000000         ;//  documentation and/or other materials provided with 
                       the
   15 00000000         ;//  distribution.
   16 00000000         ;//
   17 00000000         ;//  Neither the name of Texas Instruments Incorporated 
                       nor the names of
   18 00000000         ;//  its contributors may be used to endorse or promote 
                       products derived
   19 00000000         ;//  from this software without specific prior written p
                       ermission.
   20 00000000         ;//
   21 00000000         ;// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS A
                       ND CONTRIBUTORS
   22 00000000         ;// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLU
                       DING, BUT NOT
   23 00000000         ;// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILIT
                       Y AND FITNESS FOR
   24 00000000         ;// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHA
                       LL THE COPYRIGHT
   25 00000000         ;// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDI
                       RECT, INCIDENTAL,
   26 00000000         ;// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUD
                       ING, BUT NOT
   27 00000000         ;// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVI
                       CES; LOSS OF USE,
   28 00000000         ;// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER 
                       CAUSED AND ON ANY
   29 00000000         ;// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIA
                       BILITY, OR TORT
   30 00000000         ;// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY W
                       AY OUT OF THE USE
   31 00000000         ;// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY
                        OF SUCH DAMAGE.
   32 00000000         ;//
   33 00000000         ;// MSP432 startup file
   34 00000000         ;//
   35 00000000         ;//*****************************************************
                       ***********************



ARM Macro Assembler    Page 2 


   36 00000000         ;//-------- <<< Use Configuration Wizard in Context Menu
                        >>> ------------------
   37 00000000         ;*/
   38 00000000         
   39 00000000         ; <h> Stack Configuration
   40 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   41 00000000         ; </h>
   42 00000000         
   43 00000000 00000200 
                       Stack_Size
                               EQU              0x00000200
   44 00000000         
   45 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   46 00000000         Stack_Mem
                               SPACE            Stack_Size
   47 00000200         __initial_sp
   48 00000200         
   49 00000200         
   50 00000200         ; <h> Heap Configuration
   51 00000200         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   52 00000200         ; </h>
   53 00000200         
   54 00000200 00000000 
                       Heap_Size
                               EQU              0x00000000
   55 00000200         
   56 00000200                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   57 00000000         __heap_base
   58 00000000         Heap_Mem
                               SPACE            Heap_Size
   59 00000000         __heap_limit
   60 00000000         
   61 00000000         
   62 00000000                 PRESERVE8
   63 00000000                 THUMB
   64 00000000         
   65 00000000         
   66 00000000         ; Vector Table Mapped to Address 0 at Reset
   67 00000000         
   68 00000000                 AREA             RESET, DATA, READONLY
   69 00000000                 EXPORT           __Vectors
   70 00000000                 EXPORT           __Vectors_End
   71 00000000                 EXPORT           __Vectors_Size
   72 00000000         
   73 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   74 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   75 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   76 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   77 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   78 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            



ARM Macro Assembler    Page 3 


   79 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   80 0000001C 00000000        DCD              0           ; Reserved
   81 00000020 00000000        DCD              0           ; Reserved
   82 00000024 00000000        DCD              0           ; Reserved
   83 00000028 00000000        DCD              0           ; Reserved
   84 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   85 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   86 00000034 00000000        DCD              0           ; Reserved
   87 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   88 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   89 00000040         
   90 00000040         ; External Interrupts
   91 00000040 00000000        DCD              PSS_IRQHandler ;  0:  PSS Inter
                                                            rupt
   92 00000044 00000000        DCD              CS_IRQHandler ;  1:  CS Interru
                                                            pt 
   93 00000048 00000000        DCD              PCM_IRQHandler ;  2:  PCM Inter
                                                            rupt
   94 0000004C 00000000        DCD              WDT_A_IRQHandler ;  3:  WDT_A I
                                                            nterrupt
   95 00000050 00000000        DCD              FPU_IRQHandler ;  4:  FPU Inter
                                                            rupt
   96 00000054 00000000        DCD              FLCTL_IRQHandler ;  5:  Flash C
                                                            ontroller Interrupt
                                                            
   97 00000058 00000000        DCD              COMP_E0_IRQHandler ;  6:  COMP_
                                                            E0 Interrupt
   98 0000005C 00000000        DCD              COMP_E1_IRQHandler ;  7:  COMP_
                                                            E1 Interrupt
   99 00000060 00000000        DCD              TA0_0_IRQHandler ;  8:  TA0_0 I
                                                            nterrupt
  100 00000064 00000000        DCD              TA0_N_IRQHandler ;  9:  TA0_N I
                                                            nterrupt
  101 00000068 00000000        DCD              TA1_0_IRQHandler ; 10:  TA1_0 I
                                                            nterrupt
  102 0000006C 00000000        DCD              TA1_N_IRQHandler ; 11:  TA1_N I
                                                            nterrupt
  103 00000070 00000000        DCD              TA2_0_IRQHandler ; 12:  TA2_0 I
                                                            nterrupt
  104 00000074 00000000        DCD              TA2_N_IRQHandler ; 13:  TA2_N I
                                                            nterrupt
  105 00000078 00000000        DCD              TA3_0_IRQHandler ; 14:  TA3_0 I
                                                            nterrupt
  106 0000007C 00000000        DCD              TA3_N_IRQHandler ; 15:  TA3_N I
                                                            nterrupt
  107 00000080 00000000        DCD              EUSCIA0_IRQHandler ; 16:  EUSCI
                                                            A0 Interrupt
  108 00000084 00000000        DCD              EUSCIA1_IRQHandler ; 17:  EUSCI
                                                            A1 Interrupt
  109 00000088 00000000        DCD              EUSCIA2_IRQHandler ; 18:  EUSCI
                                                            A2 Interrupt
  110 0000008C 00000000        DCD              EUSCIA3_IRQHandler ; 19:  EUSCI
                                                            A3 Interrupt
  111 00000090 00000000        DCD              EUSCIB0_IRQHandler ; 20:  EUSCI
                                                            B0 Interrupt



ARM Macro Assembler    Page 4 


  112 00000094 00000000        DCD              EUSCIB1_IRQHandler ; 21:  EUSCI
                                                            B1 Interrupt
  113 00000098 00000000        DCD              EUSCIB2_IRQHandler ; 22:  EUSCI
                                                            B2 Interrupt
  114 0000009C 00000000        DCD              EUSCIB3_IRQHandler ; 23:  EUSCI
                                                            B3 Interrupt
  115 000000A0 00000000        DCD              ADC14_IRQHandler ; 24:  ADC14 I
                                                            nterrupt
  116 000000A4 00000000        DCD              T32_INT1_IRQHandler ; 25:  T32_
                                                            INT1 Interrupt
  117 000000A8 00000000        DCD              T32_INT2_IRQHandler ; 26:  T32_
                                                            INT2 Interrupt
  118 000000AC 00000000        DCD              T32_INTC_IRQHandler ; 27:  T32_
                                                            INTC Interrupt
  119 000000B0 00000000        DCD              AES256_IRQHandler ; 28:  AES256
                                                             Interrupt
  120 000000B4 00000000        DCD              RTC_C_IRQHandler ; 29:  RTC_C I
                                                            nterrupt
  121 000000B8 00000000        DCD              DMA_ERR_IRQHandler ; 30:  DMA_E
                                                            RR Interrupt
  122 000000BC 00000000        DCD              DMA_INT3_IRQHandler ; 31:  DMA_
                                                            INT3 Interrupt
  123 000000C0 00000000        DCD              DMA_INT2_IRQHandler ; 32:  DMA_
                                                            INT2 Interrupt
  124 000000C4 00000000        DCD              DMA_INT1_IRQHandler ; 33:  DMA_
                                                            INT1 Interrupt
  125 000000C8 00000000        DCD              DMA_INT0_IRQHandler ; 34:  DMA_
                                                            INT0 Interrupt
  126 000000CC 00000000        DCD              PORT1_IRQHandler ; 35:  Port1 I
                                                            nterrupt
  127 000000D0 00000000        DCD              PORT2_IRQHandler ; 36:  Port2 I
                                                            nterrupt
  128 000000D4 00000000        DCD              PORT3_IRQHandler ; 37:  Port3 I
                                                            nterrupt
  129 000000D8 00000000        DCD              PORT4_IRQHandler ; 38:  Port4 I
                                                            nterrupt
  130 000000DC 00000000        DCD              PORT5_IRQHandler ; 39:  Port5 I
                                                            nterrupt
  131 000000E0 00000000        DCD              PORT6_IRQHandler ; 40:  Port6 I
                                                            nterrupt
  132 000000E4 00000000        DCD              0           ; 41:  Reserved    
                                                             
  133 000000E8 00000000        DCD              0           ; 42:  Reserved    
                                                             
  134 000000EC 00000000        DCD              0           ; 43:  Reserved    
                                                             
  135 000000F0 00000000        DCD              0           ; 44:  Reserved    
                                                             
  136 000000F4 00000000        DCD              0           ; 45:  Reserved    
                                                             
  137 000000F8 00000000        DCD              0           ; 46:  Reserved    
                                                             
  138 000000FC 00000000        DCD              0           ; 47:  Reserved    
                                                             
  139 00000100 00000000        DCD              0           ; 48:  Reserved    
                                                             
  140 00000104 00000000        DCD              0           ; 49:  Reserved    
                                                             
  141 00000108 00000000        DCD              0           ; 50:  Reserved    



ARM Macro Assembler    Page 5 


                                                             
  142 0000010C 00000000        DCD              0           ; 51:  Reserved    
                                                             
  143 00000110 00000000        DCD              0           ; 52:  Reserved    
                                                             
  144 00000114 00000000        DCD              0           ; 53:  Reserved    
                                                             
  145 00000118 00000000        DCD              0           ; 54:  Reserved    
                                                             
  146 0000011C 00000000        DCD              0           ; 55:  Reserved    
                                                             
  147 00000120 00000000        DCD              0           ; 56:  Reserved    
                                                             
  148 00000124 00000000        DCD              0           ; 57:  Reserved    
                                                             
  149 00000128 00000000        DCD              0           ; 58:  Reserved    
                                                             
  150 0000012C 00000000        DCD              0           ; 59:  Reserved    
                                                             
  151 00000130 00000000        DCD              0           ; 60:  Reserved    
                                                             
  152 00000134 00000000        DCD              0           ; 61:  Reserved    
                                                             
  153 00000138 00000000        DCD              0           ; 62:  Reserved    
                                                             
  154 0000013C 00000000        DCD              0           ; 63:  Reserved    
                                                             
  155 00000140 00000000        DCD              0           ; 64:  Reserved    
                                                             
  156 00000144         
  157 00000144         __Vectors_End
  158 00000144         
  159 00000144 00000144 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  160 00000144         
  161 00000144                 AREA             |.text|, CODE, READONLY
  162 00000000         
  163 00000000         
  164 00000000         ; Reset Handler
  165 00000000         
  166 00000000         Reset_Handler
                               PROC
  167 00000000                 EXPORT           Reset_Handler             [WEAK
]
  168 00000000                 IMPORT           SystemInit
  169 00000000                 IMPORT           __main
  170 00000000 4806            LDR              R0, =SystemInit
  171 00000002 4780            BLX              R0
  172 00000004 4806            LDR              R0, =__main
  173 00000006 4700            BX               R0
  174 00000008                 ENDP
  175 00000008         
  176 00000008         
  177 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  178 00000008         
  179 00000008         NMI_Handler
                               PROC



ARM Macro Assembler    Page 6 


  180 00000008                 EXPORT           NMI_Handler               [WEAK
]
  181 00000008 E7FE            B                .
  182 0000000A                 ENDP
  184 0000000A         HardFault_Handler
                               PROC
  185 0000000A                 EXPORT           HardFault_Handler         [WEAK
]
  186 0000000A E7FE            B                .
  187 0000000C                 ENDP
  189 0000000C         MemManage_Handler
                               PROC
  190 0000000C                 EXPORT           MemManage_Handler         [WEAK
]
  191 0000000C E7FE            B                .
  192 0000000E                 ENDP
  194 0000000E         BusFault_Handler
                               PROC
  195 0000000E                 EXPORT           BusFault_Handler          [WEAK
]
  196 0000000E E7FE            B                .
  197 00000010                 ENDP
  199 00000010         UsageFault_Handler
                               PROC
  200 00000010                 EXPORT           UsageFault_Handler        [WEAK
]
  201 00000010 E7FE            B                .
  202 00000012                 ENDP
  203 00000012         SVC_Handler
                               PROC
  204 00000012                 EXPORT           SVC_Handler               [WEAK
]
  205 00000012 E7FE            B                .
  206 00000014                 ENDP
  208 00000014         DebugMon_Handler
                               PROC
  209 00000014                 EXPORT           DebugMon_Handler          [WEAK
]
  210 00000014 E7FE            B                .
  211 00000016                 ENDP
  212 00000016         PendSV_Handler
                               PROC
  213 00000016                 EXPORT           PendSV_Handler            [WEAK
]
  214 00000016 E7FE            B                .
  215 00000018                 ENDP
  216 00000018         SysTick_Handler
                               PROC
  217 00000018                 EXPORT           SysTick_Handler           [WEAK
]
  218 00000018 E7FE            B                .
  219 0000001A                 ENDP
  220 0000001A         
  221 0000001A         Default_Handler
                               PROC
  222 0000001A                 EXPORT           PSS_IRQHandler            [WEAK
]
  223 0000001A                 EXPORT           CS_IRQHandler             [WEAK
]



ARM Macro Assembler    Page 7 


  224 0000001A                 EXPORT           PCM_IRQHandler            [WEAK
]
  225 0000001A                 EXPORT           WDT_A_IRQHandler          [WEAK
]
  226 0000001A                 EXPORT           FPU_IRQHandler            [WEAK
]
  227 0000001A                 EXPORT           FLCTL_IRQHandler          [WEAK
]
  228 0000001A                 EXPORT           COMP_E0_IRQHandler        [WEAK
]
  229 0000001A                 EXPORT           COMP_E1_IRQHandler        [WEAK
]
  230 0000001A                 EXPORT           TA0_0_IRQHandler          [WEAK
]
  231 0000001A                 EXPORT           TA0_N_IRQHandler          [WEAK
]
  232 0000001A                 EXPORT           TA1_0_IRQHandler          [WEAK
]
  233 0000001A                 EXPORT           TA1_N_IRQHandler          [WEAK
]
  234 0000001A                 EXPORT           TA2_0_IRQHandler          [WEAK
]
  235 0000001A                 EXPORT           TA2_N_IRQHandler          [WEAK
]
  236 0000001A                 EXPORT           TA3_0_IRQHandler          [WEAK
]
  237 0000001A                 EXPORT           TA3_N_IRQHandler          [WEAK
]
  238 0000001A                 EXPORT           EUSCIA0_IRQHandler        [WEAK
]
  239 0000001A                 EXPORT           EUSCIA1_IRQHandler        [WEAK
]
  240 0000001A                 EXPORT           EUSCIA2_IRQHandler        [WEAK
]
  241 0000001A                 EXPORT           EUSCIA3_IRQHandler        [WEAK
]
  242 0000001A                 EXPORT           EUSCIB0_IRQHandler        [WEAK
]
  243 0000001A                 EXPORT           EUSCIB1_IRQHandler        [WEAK
]
  244 0000001A                 EXPORT           EUSCIB2_IRQHandler        [WEAK
]
  245 0000001A                 EXPORT           EUSCIB3_IRQHandler        [WEAK
]
  246 0000001A                 EXPORT           ADC14_IRQHandler          [WEAK
]
  247 0000001A                 EXPORT           T32_INT1_IRQHandler       [WEAK
]
  248 0000001A                 EXPORT           T32_INT2_IRQHandler       [WEAK
]
  249 0000001A                 EXPORT           T32_INTC_IRQHandler       [WEAK
]
  250 0000001A                 EXPORT           AES256_IRQHandler         [WEAK
]
  251 0000001A                 EXPORT           RTC_C_IRQHandler          [WEAK
]
  252 0000001A                 EXPORT           DMA_ERR_IRQHandler        [WEAK
]
  253 0000001A                 EXPORT           DMA_INT3_IRQHandler       [WEAK



ARM Macro Assembler    Page 8 


]
  254 0000001A                 EXPORT           DMA_INT2_IRQHandler       [WEAK
]
  255 0000001A                 EXPORT           DMA_INT1_IRQHandler       [WEAK
]
  256 0000001A                 EXPORT           DMA_INT0_IRQHandler       [WEAK
]
  257 0000001A                 EXPORT           PORT1_IRQHandler          [WEAK
]
  258 0000001A                 EXPORT           PORT2_IRQHandler          [WEAK
]
  259 0000001A                 EXPORT           PORT3_IRQHandler          [WEAK
]
  260 0000001A                 EXPORT           PORT4_IRQHandler          [WEAK
]
  261 0000001A                 EXPORT           PORT5_IRQHandler          [WEAK
]
  262 0000001A                 EXPORT           PORT6_IRQHandler          [WEAK
]
  263 0000001A         
  264 0000001A         PSS_IRQHandler
  265 0000001A         CS_IRQHandler
  266 0000001A         PCM_IRQHandler
  267 0000001A         WDT_A_IRQHandler
  268 0000001A         FPU_IRQHandler
  269 0000001A         FLCTL_IRQHandler
  270 0000001A         COMP_E0_IRQHandler
  271 0000001A         COMP_E1_IRQHandler
  272 0000001A         TA0_0_IRQHandler
  273 0000001A         TA0_N_IRQHandler
  274 0000001A         TA1_0_IRQHandler
  275 0000001A         TA1_N_IRQHandler
  276 0000001A         TA2_0_IRQHandler
  277 0000001A         TA2_N_IRQHandler
  278 0000001A         TA3_0_IRQHandler
  279 0000001A         TA3_N_IRQHandler
  280 0000001A         EUSCIA0_IRQHandler
  281 0000001A         EUSCIA1_IRQHandler
  282 0000001A         EUSCIA2_IRQHandler
  283 0000001A         EUSCIA3_IRQHandler
  284 0000001A         EUSCIB0_IRQHandler
  285 0000001A         EUSCIB1_IRQHandler
  286 0000001A         EUSCIB2_IRQHandler
  287 0000001A         EUSCIB3_IRQHandler
  288 0000001A         ADC14_IRQHandler
  289 0000001A         T32_INT1_IRQHandler
  290 0000001A         T32_INT2_IRQHandler
  291 0000001A         T32_INTC_IRQHandler
  292 0000001A         AES256_IRQHandler
  293 0000001A         RTC_C_IRQHandler
  294 0000001A         DMA_ERR_IRQHandler
  295 0000001A         DMA_INT3_IRQHandler
  296 0000001A         DMA_INT2_IRQHandler
  297 0000001A         DMA_INT1_IRQHandler
  298 0000001A         DMA_INT0_IRQHandler
  299 0000001A         PORT1_IRQHandler
  300 0000001A         PORT2_IRQHandler
  301 0000001A         PORT3_IRQHandler
  302 0000001A         PORT4_IRQHandler



ARM Macro Assembler    Page 9 


  303 0000001A         PORT5_IRQHandler
  304 0000001A         PORT6_IRQHandler
  305 0000001A E7FE            B                .
  306 0000001C                 ENDP
  307 0000001C         
  308 0000001C                 ALIGN
  309 0000001C         
  310 0000001C         
  311 0000001C         ; User Initial Stack & Heap
  312 0000001C         
  313 0000001C                 IF               :DEF:__MICROLIB
  314 0000001C         
  315 0000001C                 EXPORT           __initial_sp
  316 0000001C                 EXPORT           __heap_base
  317 0000001C                 EXPORT           __heap_limit
  318 0000001C         
  319 0000001C                 ELSE
  334                          ENDIF
  335 0000001C         
  336 0000001C         
  337 0000001C                 END
              00000000 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M4.fp --apcs=int
erwork --depend=.\build\startup_msp432p401r_uvision.d -o.\build\startup_msp432p
401r_uvision.o -I.\RTE\_my_project -IE:\keil_v5\Arm\Packs\ARM\CMSIS\5.5.1\CMSIS
\Core\Include -IE:\keil_v5\Arm\Packs\TexasInstruments\MSP432P4xx_DFP\3.2.6\Devi
ce\Include --predefine="__MICROLIB SETA 1" --predefine="__UVISION_VERSION SETA 
528" --predefine="_RTE_ SETA 1" --predefine="__MSP432P401R__ SETA 1" --list=.\s
tartup_msp432p401r_uvision.lst startup_msp432p401r_uvision.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 45 in file startup_msp432p401r_uvision.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 46 in file startup_msp432p401r_uvision.s
   Uses
      None
Comment: Stack_Mem unused
__initial_sp 00000200

Symbol: __initial_sp
   Definitions
      At line 47 in file startup_msp432p401r_uvision.s
   Uses
      At line 73 in file startup_msp432p401r_uvision.s
      At line 315 in file startup_msp432p401r_uvision.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 56 in file startup_msp432p401r_uvision.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 58 in file startup_msp432p401r_uvision.s
   Uses
      None
Comment: Heap_Mem unused
__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 57 in file startup_msp432p401r_uvision.s
   Uses
      At line 316 in file startup_msp432p401r_uvision.s
Comment: __heap_base used once
__heap_limit 00000000

Symbol: __heap_limit
   Definitions
      At line 59 in file startup_msp432p401r_uvision.s
   Uses
      At line 317 in file startup_msp432p401r_uvision.s
Comment: __heap_limit used once
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 68 in file startup_msp432p401r_uvision.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 73 in file startup_msp432p401r_uvision.s
   Uses
      At line 69 in file startup_msp432p401r_uvision.s
      At line 159 in file startup_msp432p401r_uvision.s

__Vectors_End 00000144

Symbol: __Vectors_End
   Definitions
      At line 157 in file startup_msp432p401r_uvision.s
   Uses
      At line 70 in file startup_msp432p401r_uvision.s
      At line 159 in file startup_msp432p401r_uvision.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 161 in file startup_msp432p401r_uvision.s
   Uses
      None
Comment: .text unused
ADC14_IRQHandler 0000001A

Symbol: ADC14_IRQHandler
   Definitions
      At line 288 in file startup_msp432p401r_uvision.s
   Uses
      At line 115 in file startup_msp432p401r_uvision.s
      At line 246 in file startup_msp432p401r_uvision.s

AES256_IRQHandler 0000001A

Symbol: AES256_IRQHandler
   Definitions
      At line 292 in file startup_msp432p401r_uvision.s
   Uses
      At line 119 in file startup_msp432p401r_uvision.s
      At line 250 in file startup_msp432p401r_uvision.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 194 in file startup_msp432p401r_uvision.s
   Uses
      At line 78 in file startup_msp432p401r_uvision.s
      At line 195 in file startup_msp432p401r_uvision.s

COMP_E0_IRQHandler 0000001A

Symbol: COMP_E0_IRQHandler
   Definitions
      At line 270 in file startup_msp432p401r_uvision.s
   Uses
      At line 97 in file startup_msp432p401r_uvision.s
      At line 228 in file startup_msp432p401r_uvision.s

COMP_E1_IRQHandler 0000001A

Symbol: COMP_E1_IRQHandler
   Definitions
      At line 271 in file startup_msp432p401r_uvision.s
   Uses
      At line 98 in file startup_msp432p401r_uvision.s
      At line 229 in file startup_msp432p401r_uvision.s

CS_IRQHandler 0000001A

Symbol: CS_IRQHandler
   Definitions
      At line 265 in file startup_msp432p401r_uvision.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 92 in file startup_msp432p401r_uvision.s
      At line 223 in file startup_msp432p401r_uvision.s

DMA_ERR_IRQHandler 0000001A

Symbol: DMA_ERR_IRQHandler
   Definitions
      At line 294 in file startup_msp432p401r_uvision.s
   Uses
      At line 121 in file startup_msp432p401r_uvision.s
      At line 252 in file startup_msp432p401r_uvision.s

DMA_INT0_IRQHandler 0000001A

Symbol: DMA_INT0_IRQHandler
   Definitions
      At line 298 in file startup_msp432p401r_uvision.s
   Uses
      At line 125 in file startup_msp432p401r_uvision.s
      At line 256 in file startup_msp432p401r_uvision.s

DMA_INT1_IRQHandler 0000001A

Symbol: DMA_INT1_IRQHandler
   Definitions
      At line 297 in file startup_msp432p401r_uvision.s
   Uses
      At line 124 in file startup_msp432p401r_uvision.s
      At line 255 in file startup_msp432p401r_uvision.s

DMA_INT2_IRQHandler 0000001A

Symbol: DMA_INT2_IRQHandler
   Definitions
      At line 296 in file startup_msp432p401r_uvision.s
   Uses
      At line 123 in file startup_msp432p401r_uvision.s
      At line 254 in file startup_msp432p401r_uvision.s

DMA_INT3_IRQHandler 0000001A

Symbol: DMA_INT3_IRQHandler
   Definitions
      At line 295 in file startup_msp432p401r_uvision.s
   Uses
      At line 122 in file startup_msp432p401r_uvision.s
      At line 253 in file startup_msp432p401r_uvision.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 208 in file startup_msp432p401r_uvision.s
   Uses
      At line 85 in file startup_msp432p401r_uvision.s
      At line 209 in file startup_msp432p401r_uvision.s

Default_Handler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: Default_Handler
   Definitions
      At line 221 in file startup_msp432p401r_uvision.s
   Uses
      None
Comment: Default_Handler unused
EUSCIA0_IRQHandler 0000001A

Symbol: EUSCIA0_IRQHandler
   Definitions
      At line 280 in file startup_msp432p401r_uvision.s
   Uses
      At line 107 in file startup_msp432p401r_uvision.s
      At line 238 in file startup_msp432p401r_uvision.s

EUSCIA1_IRQHandler 0000001A

Symbol: EUSCIA1_IRQHandler
   Definitions
      At line 281 in file startup_msp432p401r_uvision.s
   Uses
      At line 108 in file startup_msp432p401r_uvision.s
      At line 239 in file startup_msp432p401r_uvision.s

EUSCIA2_IRQHandler 0000001A

Symbol: EUSCIA2_IRQHandler
   Definitions
      At line 282 in file startup_msp432p401r_uvision.s
   Uses
      At line 109 in file startup_msp432p401r_uvision.s
      At line 240 in file startup_msp432p401r_uvision.s

EUSCIA3_IRQHandler 0000001A

Symbol: EUSCIA3_IRQHandler
   Definitions
      At line 283 in file startup_msp432p401r_uvision.s
   Uses
      At line 110 in file startup_msp432p401r_uvision.s
      At line 241 in file startup_msp432p401r_uvision.s

EUSCIB0_IRQHandler 0000001A

Symbol: EUSCIB0_IRQHandler
   Definitions
      At line 284 in file startup_msp432p401r_uvision.s
   Uses
      At line 111 in file startup_msp432p401r_uvision.s
      At line 242 in file startup_msp432p401r_uvision.s

EUSCIB1_IRQHandler 0000001A

Symbol: EUSCIB1_IRQHandler
   Definitions
      At line 285 in file startup_msp432p401r_uvision.s
   Uses
      At line 112 in file startup_msp432p401r_uvision.s
      At line 243 in file startup_msp432p401r_uvision.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


EUSCIB2_IRQHandler 0000001A

Symbol: EUSCIB2_IRQHandler
   Definitions
      At line 286 in file startup_msp432p401r_uvision.s
   Uses
      At line 113 in file startup_msp432p401r_uvision.s
      At line 244 in file startup_msp432p401r_uvision.s

EUSCIB3_IRQHandler 0000001A

Symbol: EUSCIB3_IRQHandler
   Definitions
      At line 287 in file startup_msp432p401r_uvision.s
   Uses
      At line 114 in file startup_msp432p401r_uvision.s
      At line 245 in file startup_msp432p401r_uvision.s

FLCTL_IRQHandler 0000001A

Symbol: FLCTL_IRQHandler
   Definitions
      At line 269 in file startup_msp432p401r_uvision.s
   Uses
      At line 96 in file startup_msp432p401r_uvision.s
      At line 227 in file startup_msp432p401r_uvision.s

FPU_IRQHandler 0000001A

Symbol: FPU_IRQHandler
   Definitions
      At line 268 in file startup_msp432p401r_uvision.s
   Uses
      At line 95 in file startup_msp432p401r_uvision.s
      At line 226 in file startup_msp432p401r_uvision.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 184 in file startup_msp432p401r_uvision.s
   Uses
      At line 76 in file startup_msp432p401r_uvision.s
      At line 185 in file startup_msp432p401r_uvision.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 189 in file startup_msp432p401r_uvision.s
   Uses
      At line 77 in file startup_msp432p401r_uvision.s
      At line 190 in file startup_msp432p401r_uvision.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 179 in file startup_msp432p401r_uvision.s
   Uses
      At line 75 in file startup_msp432p401r_uvision.s
      At line 180 in file startup_msp432p401r_uvision.s

PCM_IRQHandler 0000001A

Symbol: PCM_IRQHandler
   Definitions
      At line 266 in file startup_msp432p401r_uvision.s
   Uses
      At line 93 in file startup_msp432p401r_uvision.s
      At line 224 in file startup_msp432p401r_uvision.s

PORT1_IRQHandler 0000001A

Symbol: PORT1_IRQHandler
   Definitions
      At line 299 in file startup_msp432p401r_uvision.s
   Uses
      At line 126 in file startup_msp432p401r_uvision.s
      At line 257 in file startup_msp432p401r_uvision.s

PORT2_IRQHandler 0000001A

Symbol: PORT2_IRQHandler
   Definitions
      At line 300 in file startup_msp432p401r_uvision.s
   Uses
      At line 127 in file startup_msp432p401r_uvision.s
      At line 258 in file startup_msp432p401r_uvision.s

PORT3_IRQHandler 0000001A

Symbol: PORT3_IRQHandler
   Definitions
      At line 301 in file startup_msp432p401r_uvision.s
   Uses
      At line 128 in file startup_msp432p401r_uvision.s
      At line 259 in file startup_msp432p401r_uvision.s

PORT4_IRQHandler 0000001A

Symbol: PORT4_IRQHandler
   Definitions
      At line 302 in file startup_msp432p401r_uvision.s
   Uses
      At line 129 in file startup_msp432p401r_uvision.s
      At line 260 in file startup_msp432p401r_uvision.s

PORT5_IRQHandler 0000001A

Symbol: PORT5_IRQHandler
   Definitions
      At line 303 in file startup_msp432p401r_uvision.s
   Uses
      At line 130 in file startup_msp432p401r_uvision.s
      At line 261 in file startup_msp432p401r_uvision.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

PORT6_IRQHandler 0000001A

Symbol: PORT6_IRQHandler
   Definitions
      At line 304 in file startup_msp432p401r_uvision.s
   Uses
      At line 131 in file startup_msp432p401r_uvision.s
      At line 262 in file startup_msp432p401r_uvision.s

PSS_IRQHandler 0000001A

Symbol: PSS_IRQHandler
   Definitions
      At line 264 in file startup_msp432p401r_uvision.s
   Uses
      At line 91 in file startup_msp432p401r_uvision.s
      At line 222 in file startup_msp432p401r_uvision.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 212 in file startup_msp432p401r_uvision.s
   Uses
      At line 87 in file startup_msp432p401r_uvision.s
      At line 213 in file startup_msp432p401r_uvision.s

RTC_C_IRQHandler 0000001A

Symbol: RTC_C_IRQHandler
   Definitions
      At line 293 in file startup_msp432p401r_uvision.s
   Uses
      At line 120 in file startup_msp432p401r_uvision.s
      At line 251 in file startup_msp432p401r_uvision.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 166 in file startup_msp432p401r_uvision.s
   Uses
      At line 74 in file startup_msp432p401r_uvision.s
      At line 167 in file startup_msp432p401r_uvision.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 203 in file startup_msp432p401r_uvision.s
   Uses
      At line 84 in file startup_msp432p401r_uvision.s
      At line 204 in file startup_msp432p401r_uvision.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 216 in file startup_msp432p401r_uvision.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 88 in file startup_msp432p401r_uvision.s
      At line 217 in file startup_msp432p401r_uvision.s

T32_INT1_IRQHandler 0000001A

Symbol: T32_INT1_IRQHandler
   Definitions
      At line 289 in file startup_msp432p401r_uvision.s
   Uses
      At line 116 in file startup_msp432p401r_uvision.s
      At line 247 in file startup_msp432p401r_uvision.s

T32_INT2_IRQHandler 0000001A

Symbol: T32_INT2_IRQHandler
   Definitions
      At line 290 in file startup_msp432p401r_uvision.s
   Uses
      At line 117 in file startup_msp432p401r_uvision.s
      At line 248 in file startup_msp432p401r_uvision.s

T32_INTC_IRQHandler 0000001A

Symbol: T32_INTC_IRQHandler
   Definitions
      At line 291 in file startup_msp432p401r_uvision.s
   Uses
      At line 118 in file startup_msp432p401r_uvision.s
      At line 249 in file startup_msp432p401r_uvision.s

TA0_0_IRQHandler 0000001A

Symbol: TA0_0_IRQHandler
   Definitions
      At line 272 in file startup_msp432p401r_uvision.s
   Uses
      At line 99 in file startup_msp432p401r_uvision.s
      At line 230 in file startup_msp432p401r_uvision.s

TA0_N_IRQHandler 0000001A

Symbol: TA0_N_IRQHandler
   Definitions
      At line 273 in file startup_msp432p401r_uvision.s
   Uses
      At line 100 in file startup_msp432p401r_uvision.s
      At line 231 in file startup_msp432p401r_uvision.s

TA1_0_IRQHandler 0000001A

Symbol: TA1_0_IRQHandler
   Definitions
      At line 274 in file startup_msp432p401r_uvision.s
   Uses
      At line 101 in file startup_msp432p401r_uvision.s
      At line 232 in file startup_msp432p401r_uvision.s

TA1_N_IRQHandler 0000001A



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: TA1_N_IRQHandler
   Definitions
      At line 275 in file startup_msp432p401r_uvision.s
   Uses
      At line 102 in file startup_msp432p401r_uvision.s
      At line 233 in file startup_msp432p401r_uvision.s

TA2_0_IRQHandler 0000001A

Symbol: TA2_0_IRQHandler
   Definitions
      At line 276 in file startup_msp432p401r_uvision.s
   Uses
      At line 103 in file startup_msp432p401r_uvision.s
      At line 234 in file startup_msp432p401r_uvision.s

TA2_N_IRQHandler 0000001A

Symbol: TA2_N_IRQHandler
   Definitions
      At line 277 in file startup_msp432p401r_uvision.s
   Uses
      At line 104 in file startup_msp432p401r_uvision.s
      At line 235 in file startup_msp432p401r_uvision.s

TA3_0_IRQHandler 0000001A

Symbol: TA3_0_IRQHandler
   Definitions
      At line 278 in file startup_msp432p401r_uvision.s
   Uses
      At line 105 in file startup_msp432p401r_uvision.s
      At line 236 in file startup_msp432p401r_uvision.s

TA3_N_IRQHandler 0000001A

Symbol: TA3_N_IRQHandler
   Definitions
      At line 279 in file startup_msp432p401r_uvision.s
   Uses
      At line 106 in file startup_msp432p401r_uvision.s
      At line 237 in file startup_msp432p401r_uvision.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 199 in file startup_msp432p401r_uvision.s
   Uses
      At line 79 in file startup_msp432p401r_uvision.s
      At line 200 in file startup_msp432p401r_uvision.s

WDT_A_IRQHandler 0000001A

Symbol: WDT_A_IRQHandler
   Definitions
      At line 267 in file startup_msp432p401r_uvision.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 94 in file startup_msp432p401r_uvision.s
      At line 225 in file startup_msp432p401r_uvision.s

53 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000000

Symbol: Heap_Size
   Definitions
      At line 54 in file startup_msp432p401r_uvision.s
   Uses
      At line 58 in file startup_msp432p401r_uvision.s
Comment: Heap_Size used once
Stack_Size 00000200

Symbol: Stack_Size
   Definitions
      At line 43 in file startup_msp432p401r_uvision.s
   Uses
      At line 46 in file startup_msp432p401r_uvision.s
Comment: Stack_Size used once
__Vectors_Size 00000144

Symbol: __Vectors_Size
   Definitions
      At line 159 in file startup_msp432p401r_uvision.s
   Uses
      At line 71 in file startup_msp432p401r_uvision.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 168 in file startup_msp432p401r_uvision.s
   Uses
      At line 170 in file startup_msp432p401r_uvision.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 169 in file startup_msp432p401r_uvision.s
   Uses
      At line 172 in file startup_msp432p401r_uvision.s
Comment: __main used once
2 symbols
405 symbols in table
