Component: ARM Compiler 5.06 update 6 (build 750) Tool: armlink [4d35ed]

==============================================================================

Section Cross References

    system_msp432p401r.o(i.SystemCoreClockUpdate) refers to system_msp432p401r.o(.data) for .data
    startup_msp432p401r_uvision.o(RESET) refers to startup_msp432p401r_uvision.o(STACK) for __initial_sp
    startup_msp432p401r_uvision.o(RESET) refers to startup_msp432p401r_uvision.o(.text) for Reset_Handler
    startup_msp432p401r_uvision.o(RESET) refers to main.o(i.TA1_0_IRQHandler) for TA1_0_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to main.o(i.TA2_0_IRQHandler) for TA2_0_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to tima.o(i.TA3_N_IRQHandler) for TA3_N_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to main.o(i.EUSCIA0_IRQHandler) for EUSCIA0_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to adc.o(i.ADC14_IRQHandler) for ADC14_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to tim32.o(i.T32_INT1_IRQHandler) for T32_INT1_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to tim32.o(i.T32_INT2_IRQHandler) for T32_INT2_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to key.o(i.PORT1_IRQHandler) for PORT1_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to encoder.o(i.PORT2_IRQHandler) for PORT2_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to encoder.o(i.PORT4_IRQHandler) for PORT4_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to encoder.o(i.PORT5_IRQHandler) for PORT5_IRQHandler
    startup_msp432p401r_uvision.o(RESET) refers to encoder.o(i.PORT6_IRQHandler) for PORT6_IRQHandler
    startup_msp432p401r_uvision.o(.text) refers to system_msp432p401r.o(i.SystemInit) for SystemInit
    startup_msp432p401r_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.EUSCIA0_IRQHandler) refers to uart.o(i.UART_clearInterruptFlag) for UART_clearInterruptFlag
    main.o(i.EUSCIA0_IRQHandler) refers to uart.o(i.UART_receiveData) for UART_receiveData
    main.o(i.EUSCIA0_IRQHandler) refers to globalvar.o(.data) for usart0_rx_data
    main.o(i.TA1_0_IRQHandler) refers to encoder.o(i.SW_Encoder_Read) for SW_Encoder_Read
    main.o(i.TA1_0_IRQHandler) refers to encoder.o(i.encoder_to_speed) for encoder_to_speed
    main.o(i.TA1_0_IRQHandler) refers to motor.o(i.Motor_Set) for Motor_Set
    main.o(i.TA1_0_IRQHandler) refers to motor.o(i.Motor_PID) for Motor_PID
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.data) for encoder_left
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.data) for speed_left
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.data) for encoder_right
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.data) for speed_right
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.data) for road_flag
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.data) for road
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.data) for motor_flag
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.bss) for pid_left
    main.o(i.TA1_0_IRQHandler) refers to globalvar.o(.bss) for pid_right
    main.o(i.TA2_0_IRQHandler) refers to grayscale.o(i.Grayscale_scan) for Grayscale_scan
    main.o(i.TA2_0_IRQHandler) refers to fsm.o(i.FSM) for FSM
    main.o(i.TA2_0_IRQHandler) refers to tima.o(i.ult_start) for ult_start
    main.o(i.TA2_0_IRQHandler) refers to dflti.o(.text) for __aeabi_i2d
    main.o(i.TA2_0_IRQHandler) refers to dadd.o(.text) for __aeabi_drsub
    main.o(i.TA2_0_IRQHandler) refers to dmul.o(.text) for __aeabi_dmul
    main.o(i.TA2_0_IRQHandler) refers to dfixi.o(.text) for __aeabi_d2iz
    main.o(i.TA2_0_IRQHandler) refers to uart.o(i.UART_transmitData) for UART_transmitData
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for wait_flag
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for wait_time
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for task
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for lap
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for ult_distance
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for movement
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for road
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for last_send_flag
    main.o(i.TA2_0_IRQHandler) refers to globalvar.o(.data) for send_flag
    main.o(i.main) refers to sysinit.o(i.SysInit) for SysInit
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to delay.o(i.delay_init) for delay_init
    main.o(i.main) refers to main.o(i.system_init) for system_init
    main.o(i.main) refers to delay.o(i.delay_ms) for delay_ms
    main.o(i.main) refers to oled.o(i.OLED_ShowNum) for OLED_ShowNum
    main.o(i.main) refers to led.o(i.LED_B_Tog) for LED_B_Tog
    main.o(i.main) refers to led.o(i.LED_G_Tog) for LED_G_Tog
    main.o(i.main) refers to key.o(i.KEY_Scan) for KEY_Scan
    main.o(i.main) refers to led.o(i.BEEP_On) for BEEP_On
    main.o(i.main) refers to led.o(i.BEEP_Off) for BEEP_Off
    main.o(i.main) refers to globalvar.o(.data) for task
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to globalvar.o(.data) for send_flag
    main.o(i.main) refers to globalvar.o(.data) for motor_flag
    main.o(i.main) refers to globalvar.o(.data) for beep_flag
    main.o(i.main) refers to globalvar.o(.data) for lap
    main.o(i.main) refers to globalvar.o(.data) for ult_distance
    main.o(i.system_init) refers to led.o(i.LED_Init) for LED_Init
    main.o(i.system_init) refers to led.o(i.BEEP_Init) for BEEP_Init
    main.o(i.system_init) refers to key.o(i.KEY_Init) for KEY_Init
    main.o(i.system_init) refers to oled.o(i.OLED_Init) for OLED_Init
    main.o(i.system_init) refers to grayscale.o(i.Grayscale_Init) for Grayscale_Init
    main.o(i.system_init) refers to encoder.o(i.SW_Encoder_Init) for SW_Encoder_Init
    main.o(i.system_init) refers to motor.o(i.Motor_Init) for Motor_Init
    main.o(i.system_init) refers to tima.o(i.TimA3_Cap_Init) for TimA3_Cap_Init
    main.o(i.system_init) refers to tima.o(i.TimA2_Int_Init) for TimA2_Int_Init
    main.o(i.system_init) refers to tima.o(i.TimA1_Int_Init) for TimA1_Int_Init
    main.o(i.system_init) refers to dfixi.o(.text) for __aeabi_d2iz
    main.o(i.system_init) refers to pidcontrol.o(i.Incremental_PID_Init) for Incremental_PID_Init
    main.o(i.system_init) refers to interrupt.o(i.Interrupt_setPriority) for Interrupt_setPriority
    main.o(i.system_init) refers to gpio.o(i.GPIO_clearInterruptFlag) for GPIO_clearInterruptFlag
    main.o(i.system_init) refers to globalvar.o(.data) for movement
    main.o(i.system_init) refers to globalvar.o(.data) for movement_left
    main.o(i.system_init) refers to globalvar.o(.data) for movement_right
    main.o(i.system_init) refers to globalvar.o(.data) for small_kdiff
    main.o(i.system_init) refers to globalvar.o(.data) for medium_kdiff
    main.o(i.system_init) refers to globalvar.o(.data) for large_kdiff
    main.o(i.system_init) refers to globalvar.o(.bss) for pid_left
    main.o(i.system_init) refers to globalvar.o(.bss) for pid_right
    adc.o(i.ADC14_IRQHandler) refers to adc14.o(i.ADC14_getMultiSequenceResult) for ADC14_getMultiSequenceResult
    adc.o(i.ADC14_IRQHandler) refers to printf1.o(i.__0printf$1) for __2printf
    adc.o(i.ADC14_IRQHandler) refers to adc.o(.data) for .data
    adc.o(i.ADC_Config) refers to adc14.o(i.ADC14_configureMultiSequenceMode) for ADC14_configureMultiSequenceMode
    adc.o(i.ADC_Config) refers to adc14.o(i.ADC14_configureConversionMemory) for ADC14_configureConversionMemory
    key.o(i.KEY_Scan) refers to key.o(i.key_delay) for key_delay
    key.o(i.KEY_Scan) refers to key.o(.data) for .data
    key.o(i.PORT1_IRQHandler) refers to key.o(i.key_delay) for key_delay
    key4x4.o(i.KEY4x4_Scan) refers to key4x4.o(.data) for .data
    led.o(i.BEEP_Init) refers to globalvar.o(.data) for beep_flag
    led.o(i.LED_W_Tog) refers to led.o(i.LED_R_Tog) for LED_R_Tog
    led.o(i.LED_W_Tog) refers to led.o(i.LED_G_Tog) for LED_G_Tog
    led.o(i.LED_W_Tog) refers to led.o(i.LED_B_Tog) for LED_B_Tog
    oled.o(i.I2C_Configuration) refers to memcpya.o(.text) for __aeabi_memcpy4
    oled.o(i.I2C_Configuration) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ColorTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DisplayTurn) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_DrawBMP) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.I2C_Configuration) for I2C_Configuration
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Set_Pos) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_Set_Pos) for OLED_Set_Pos
    oled.o(i.OLED_ShowChinese) refers to oled.o(i.OLED_WR_Byte) for OLED_WR_Byte
    oled.o(i.OLED_ShowChinese) refers to oled.o(.constdata) for .constdata
    oled.o(i.OLED_ShowNum) refers to oled.o(i.oled_pow) for oled_pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowString) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_WR_Byte) refers to i2c.o(i.I2C_masterSendMultiByteStart) for I2C_masterSendMultiByteStart
    oled.o(i.OLED_WR_Byte) refers to i2c.o(i.I2C_masterSendMultiByteFinish) for I2C_masterSendMultiByteFinish
    tima.o(i.TA3_N_IRQHandler) refers to timer_a.o(i.Timer_A_getCaptureCompareCount) for Timer_A_getCaptureCompareCount
    tima.o(i.TA3_N_IRQHandler) refers to tima.o(.data) for .data
    tima.o(i.TA3_N_IRQHandler) refers to globalvar.o(.data) for ult_distance
    tima.o(i.TimA0_PWM_Init) refers to memcpya.o(.text) for __aeabi_memcpy4
    tima.o(i.TimA0_PWM_Init) refers to tima.o(.constdata) for .constdata
    tima.o(i.TimA3_Cap_Init) refers to gpio.o(i.GPIO_setAsOutputPin) for GPIO_setAsOutputPin
    tima.o(i.TimA3_Cap_Init) refers to memcpya.o(.text) for __aeabi_memcpy4
    tima.o(i.TimA3_Cap_Init) refers to tima.o(.constdata) for .constdata
    tima.o(i.ult_start) refers to gpio.o(i.GPIO_setOutputHighOnPin) for GPIO_setOutputHighOnPin
    tima.o(i.ult_start) refers to delay.o(i.delay_us) for delay_us
    tima.o(i.ult_start) refers to gpio.o(i.GPIO_setOutputLowOnPin) for GPIO_setOutputLowOnPin
    usart.o(i.fputc) refers to uart.o(i.UART_transmitData) for UART_transmitData
    usart.o(i.uart2_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    usart.o(i.uart2_init) refers to baudrate_calculate.o(i.eusci_calcBaudDividers) for eusci_calcBaudDividers
    usart.o(i.uart2_init) refers to uart.o(i.UART_initModule) for UART_initModule
    usart.o(i.uart2_init) refers to uart.o(i.UART_enableInterrupt) for UART_enableInterrupt
    usart.o(i.uart2_init) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    usart.o(i.uart2_init) refers to usart.o(.constdata) for .constdata
    usart.o(i.uart_init) refers to memcpya.o(.text) for __aeabi_memcpy4
    usart.o(i.uart_init) refers to baudrate_calculate.o(i.eusci_calcBaudDividers) for eusci_calcBaudDividers
    usart.o(i.uart_init) refers to uart.o(i.UART_initModule) for UART_initModule
    usart.o(i.uart_init) refers to uart.o(i.UART_enableInterrupt) for UART_enableInterrupt
    usart.o(i.uart_init) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    usart.o(i.uart_init) refers to usart.o(.constdata) for .constdata
    sysinit.o(i.SysInit) refers to cs.o(i.CS_setExternalClockSourceFrequency) for CS_setExternalClockSourceFrequency
    sysinit.o(i.SysInit) refers to cs.o(i.CS_startHFXT) for CS_startHFXT
    sysinit.o(i.SysInit) refers to cs.o(i.CS_startLFXT) for CS_startLFXT
    delay.o(i.delay_init) refers to cs.o(i.CS_getMCLK) for CS_getMCLK
    delay.o(i.delay_init) refers to delay.o(.data) for .data
    delay.o(i.delay_ms) refers to delay.o(i.delay_us) for delay_us
    delay.o(i.delay_us) refers to delay.o(.data) for .data
    baudrate_calculate.o(i.eusci_calcBaudDividers) refers to cs.o(i.CS_getSMCLK) for CS_getSMCLK
    baudrate_calculate.o(i.eusci_calcBaudDividers) refers to cs.o(i.CS_getACLK) for CS_getACLK
    baudrate_calculate.o(i.eusci_calcBaudDividers) refers to baudrate_calculate.o(i.bitPosition) for bitPosition
    encoder.o(i.PORT2_IRQHandler) refers to gpio.o(i.GPIO_getEnabledInterruptStatus) for GPIO_getEnabledInterruptStatus
    encoder.o(i.PORT2_IRQHandler) refers to gpio.o(i.GPIO_clearInterruptFlag) for GPIO_clearInterruptFlag
    encoder.o(i.PORT2_IRQHandler) refers to gpio.o(i.GPIO_getInputPinValue) for GPIO_getInputPinValue
    encoder.o(i.PORT2_IRQHandler) refers to encoder.o(.data) for .data
    encoder.o(i.PORT4_IRQHandler) refers to gpio.o(i.GPIO_getEnabledInterruptStatus) for GPIO_getEnabledInterruptStatus
    encoder.o(i.PORT4_IRQHandler) refers to gpio.o(i.GPIO_clearInterruptFlag) for GPIO_clearInterruptFlag
    encoder.o(i.PORT4_IRQHandler) refers to gpio.o(i.GPIO_getInputPinValue) for GPIO_getInputPinValue
    encoder.o(i.PORT4_IRQHandler) refers to encoder.o(.data) for .data
    encoder.o(i.PORT5_IRQHandler) refers to gpio.o(i.GPIO_getEnabledInterruptStatus) for GPIO_getEnabledInterruptStatus
    encoder.o(i.PORT5_IRQHandler) refers to gpio.o(i.GPIO_clearInterruptFlag) for GPIO_clearInterruptFlag
    encoder.o(i.PORT5_IRQHandler) refers to gpio.o(i.GPIO_getInputPinValue) for GPIO_getInputPinValue
    encoder.o(i.PORT5_IRQHandler) refers to encoder.o(.data) for .data
    encoder.o(i.PORT6_IRQHandler) refers to gpio.o(i.GPIO_getEnabledInterruptStatus) for GPIO_getEnabledInterruptStatus
    encoder.o(i.PORT6_IRQHandler) refers to gpio.o(i.GPIO_clearInterruptFlag) for GPIO_clearInterruptFlag
    encoder.o(i.PORT6_IRQHandler) refers to gpio.o(i.GPIO_getInputPinValue) for GPIO_getInputPinValue
    encoder.o(i.PORT6_IRQHandler) refers to encoder.o(.data) for .data
    encoder.o(i.SW_Encoder_Clear) refers to encoder.o(.data) for .data
    encoder.o(i.SW_Encoder_Init) refers to gpio.o(i.GPIO_enableInterrupt) for GPIO_enableInterrupt
    encoder.o(i.SW_Encoder_Init) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    encoder.o(i.SW_Encoder_Init) refers to gpio.o(i.GPIO_interruptEdgeSelect) for GPIO_interruptEdgeSelect
    encoder.o(i.SW_Encoder_Read) refers to encoder.o(i.SW_Encoder_Clear) for SW_Encoder_Clear
    encoder.o(i.SW_Encoder_Read) refers to encoder.o(.data) for .data
    encoder.o(i.SW_Encoder_Read) refers to globalvar.o(.data) for encoder_left
    encoder.o(i.SW_Encoder_Read) refers to globalvar.o(.data) for encoder_right
    motor.o(i.Motor_Init) refers to tima.o(i.TimA0_PWM_Init) for TimA0_PWM_Init
    motor.o(i.Motor_Init) refers to gpio.o(i.GPIO_setAsOutputPin) for GPIO_setAsOutputPin
    motor.o(i.Motor_PID) refers to pidcontrol.o(i.Incremental_PID_Calc) for Incremental_PID_Calc
    motor.o(i.Motor_PID) refers to motor.o(i.Motor_Set) for Motor_Set
    motor.o(i.Motor_PID) refers to globalvar.o(.data) for speed_left
    motor.o(i.Motor_PID) refers to globalvar.o(.data) for movement_left
    motor.o(i.Motor_PID) refers to globalvar.o(.bss) for pid_left
    motor.o(i.Motor_PID) refers to globalvar.o(.data) for speed_right
    motor.o(i.Motor_PID) refers to globalvar.o(.data) for movement_right
    motor.o(i.Motor_PID) refers to globalvar.o(.bss) for pid_right
    motor.o(i.Motor_Set) refers to globalvar.o(i.myabs) for myabs
    motor.o(i.Motor_Set) refers to gpio.o(i.GPIO_setOutputHighOnPin) for GPIO_setOutputHighOnPin
    motor.o(i.Motor_Set) refers to gpio.o(i.GPIO_setOutputLowOnPin) for GPIO_setOutputLowOnPin
    grayscale.o(i.Grayscale_Init) refers to gpio.o(i.GPIO_setAsInputPin) for GPIO_setAsInputPin
    grayscale.o(i.Grayscale_Judge) refers to grayscale.o(.data) for .data
    grayscale.o(i.Grayscale_Judge) refers to globalvar.o(.data) for movement
    grayscale.o(i.Grayscale_Judge) refers to globalvar.o(.data) for movement_left
    grayscale.o(i.Grayscale_Judge) refers to globalvar.o(.data) for movement_right
    grayscale.o(i.Grayscale_Judge) refers to globalvar.o(.data) for small_kdiff
    grayscale.o(i.Grayscale_Judge) refers to globalvar.o(.data) for medium_kdiff
    grayscale.o(i.Grayscale_Judge) refers to globalvar.o(.data) for large_kdiff
    grayscale.o(i.Grayscale_scan) refers to gpio.o(i.GPIO_getInputPinValue) for GPIO_getInputPinValue
    grayscale.o(i.Grayscale_scan) refers to grayscale.o(.data) for .data
    grayscale.o(i.Grayscale_scan) refers to globalvar.o(.data) for gray_state
    grayscale.o(i.Inner_Ring) refers to grayscale.o(.data) for .data
    grayscale.o(i.Inner_Ring) refers to globalvar.o(.data) for movement
    grayscale.o(i.Inner_Ring) refers to globalvar.o(.data) for movement_left
    grayscale.o(i.Inner_Ring) refers to globalvar.o(.data) for movement_right
    grayscale.o(i.Inner_Ring) refers to globalvar.o(.data) for large_kdiff
    grayscale.o(i.Inner_Ring) refers to globalvar.o(.data) for small_kdiff
    grayscale.o(i.Inner_Ring) refers to globalvar.o(.data) for medium_kdiff
    grayscale.o(i.Outer_Ring) refers to grayscale.o(.data) for .data
    grayscale.o(i.Outer_Ring) refers to globalvar.o(.data) for movement
    grayscale.o(i.Outer_Ring) refers to globalvar.o(.data) for movement_left
    grayscale.o(i.Outer_Ring) refers to globalvar.o(.data) for movement_right
    grayscale.o(i.Outer_Ring) refers to globalvar.o(.data) for small_kdiff
    grayscale.o(i.Outer_Ring) refers to globalvar.o(.data) for large_kdiff
    grayscale.o(i.Outer_Ring) refers to globalvar.o(.data) for medium_kdiff
    fsm.o(i.FSM) refers to grayscale.o(i.Grayscale_Judge) for Grayscale_Judge
    fsm.o(i.FSM) refers to led.o(i.BEEP_On) for BEEP_On
    fsm.o(i.FSM) refers to led.o(i.LED_RED_Tog) for LED_RED_Tog
    fsm.o(i.FSM) refers to led.o(i.LED_R_Tog) for LED_R_Tog
    fsm.o(i.FSM) refers to grayscale.o(i.Outer_Ring) for Outer_Ring
    fsm.o(i.FSM) refers to grayscale.o(i.Inner_Ring) for Inner_Ring
    fsm.o(i.FSM) refers to fsm.o(.data) for .data
    fsm.o(i.FSM) refers to globalvar.o(.data) for movement
    fsm.o(i.FSM) refers to grayscale.o(.data) for CHF
    fsm.o(i.FSM) refers to globalvar.o(.data) for cross_flag
    fsm.o(i.FSM) refers to globalvar.o(.data) for motor_flag
    fsm.o(i.FSM) refers to globalvar.o(.data) for send_flag
    fsm.o(i.FSM) refers to globalvar.o(.data) for wait_flag
    fsm.o(i.FSM) refers to globalvar.o(.data) for wait_time
    fsm.o(i.FSM) refers to globalvar.o(.data) for gray_state
    fsm.o(i.FSM) refers to globalvar.o(.data) for lap
    fsm.o(i.FSM) refers to globalvar.o(.data) for task
    fsm.o(i.FSM) refers to globalvar.o(.data) for road_flag
    fsm.o(i.FSM) refers to globalvar.o(.data) for road
    adc14.o(i.ADC14_configureConversionMemory) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_configureConversionMemory) refers to adc14.o(i._getIndexForMemRegister) for _getIndexForMemRegister
    adc14.o(i.ADC14_configureConversionMemory) refers to adc14.o(.constdata) for .constdata
    adc14.o(i.ADC14_configureMultiSequenceMode) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_configureMultiSequenceMode) refers to adc14.o(i._getIndexForMemRegister) for _getIndexForMemRegister
    adc14.o(i.ADC14_configureMultiSequenceMode) refers to adc14.o(.constdata) for .constdata
    adc14.o(i.ADC14_configureSingleSampleMode) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_configureSingleSampleMode) refers to adc14.o(i._getIndexForMemRegister) for _getIndexForMemRegister
    adc14.o(i.ADC14_disableComparatorWindow) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_disableComparatorWindow) refers to adc14.o(i._getIndexForMemRegister) for _getIndexForMemRegister
    adc14.o(i.ADC14_disableComparatorWindow) refers to adc14.o(.constdata) for .constdata
    adc14.o(i.ADC14_disableModule) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_disableReferenceBurst) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_disableSampleTimer) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_enableComparatorWindow) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_enableComparatorWindow) refers to adc14.o(i._getIndexForMemRegister) for _getIndexForMemRegister
    adc14.o(i.ADC14_enableComparatorWindow) refers to adc14.o(.constdata) for .constdata
    adc14.o(i.ADC14_enableConversion) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_enableReferenceBurst) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_enableSampleTimer) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_getEnabledInterruptStatus) refers to adc14.o(i.ADC14_getInterruptStatus) for ADC14_getInterruptStatus
    adc14.o(i.ADC14_getResult) refers to adc14.o(i._getIndexForMemRegister) for _getIndexForMemRegister
    adc14.o(i.ADC14_getResult) refers to adc14.o(.constdata) for .constdata
    adc14.o(i.ADC14_getResultArray) refers to adc14.o(i._getIndexForMemRegister) for _getIndexForMemRegister
    adc14.o(i.ADC14_getResultArray) refers to adc14.o(.constdata) for .constdata
    adc14.o(i.ADC14_initModule) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_registerInterrupt) refers to interrupt.o(i.Interrupt_registerInterrupt) for Interrupt_registerInterrupt
    adc14.o(i.ADC14_registerInterrupt) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    adc14.o(i.ADC14_setComparatorWindowValue) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_setPowerMode) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_setResultFormat) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_setSampleHoldTime) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_setSampleHoldTrigger) refers to adc14.o(i.ADCIsConversionRunning) for ADCIsConversionRunning
    adc14.o(i.ADC14_unregisterInterrupt) refers to interrupt.o(i.Interrupt_disableInterrupt) for Interrupt_disableInterrupt
    adc14.o(i.ADC14_unregisterInterrupt) refers to interrupt.o(i.Interrupt_unregisterInterrupt) for Interrupt_unregisterInterrupt
    cs.o(i.CS_getACLK) refers to cs.o(i._CSComputeCLKFrequency) for _CSComputeCLKFrequency
    cs.o(i.CS_getBCLK) refers to cs.o(i._CSComputeCLKFrequency) for _CSComputeCLKFrequency
    cs.o(i.CS_getDCOFrequency) refers to sysctl_a.o(i.SysCtl_A_getTLVInfo) for SysCtl_A_getTLVInfo
    cs.o(i.CS_getHSMCLK) refers to cs.o(i._CSComputeCLKFrequency) for _CSComputeCLKFrequency
    cs.o(i.CS_getMCLK) refers to cs.o(i._CSComputeCLKFrequency) for _CSComputeCLKFrequency
    cs.o(i.CS_getSMCLK) refers to cs.o(i._CSComputeCLKFrequency) for _CSComputeCLKFrequency
    cs.o(i.CS_registerInterrupt) refers to interrupt.o(i.Interrupt_registerInterrupt) for Interrupt_registerInterrupt
    cs.o(i.CS_registerInterrupt) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    cs.o(i.CS_setDCOFrequency) refers to sysctl_a.o(i.SysCtl_A_getTLVInfo) for SysCtl_A_getTLVInfo
    cs.o(i.CS_setDCOFrequency) refers to cs.o(i.CS_setDCOCenteredFrequency) for CS_setDCOCenteredFrequency
    cs.o(i.CS_setDCOFrequency) refers to cs.o(i.CS_tuneDCOFrequency) for CS_tuneDCOFrequency
    cs.o(i.CS_setExternalClockSourceFrequency) refers to cs.o(.data) for .data
    cs.o(i.CS_startHFXT) refers to cs.o(i.CS_startHFXTWithTimeout) for CS_startHFXTWithTimeout
    cs.o(i.CS_startHFXTWithTimeout) refers to sysctl_a.o(i.SysCtl_A_getNMISourceStatus) for SysCtl_A_getNMISourceStatus
    cs.o(i.CS_startHFXTWithTimeout) refers to sysctl_a.o(i.SysCtl_A_disableNMISource) for SysCtl_A_disableNMISource
    cs.o(i.CS_startHFXTWithTimeout) refers to cs.o(i._CSGetHFXTFrequency) for _CSGetHFXTFrequency
    cs.o(i.CS_startHFXTWithTimeout) refers to sysctl_a.o(i.SysCtl_A_enableNMISource) for SysCtl_A_enableNMISource
    cs.o(i.CS_startLFXT) refers to cs.o(i.CS_startLFXTWithTimeout) for CS_startLFXTWithTimeout
    cs.o(i.CS_startLFXTWithTimeout) refers to sysctl_a.o(i.SysCtl_A_getNMISourceStatus) for SysCtl_A_getNMISourceStatus
    cs.o(i.CS_startLFXTWithTimeout) refers to sysctl_a.o(i.SysCtl_A_disableNMISource) for SysCtl_A_disableNMISource
    cs.o(i.CS_startLFXTWithTimeout) refers to sysctl_a.o(i.SysCtl_A_enableNMISource) for SysCtl_A_enableNMISource
    cs.o(i.CS_unregisterInterrupt) refers to interrupt.o(i.Interrupt_disableInterrupt) for Interrupt_disableInterrupt
    cs.o(i.CS_unregisterInterrupt) refers to interrupt.o(i.Interrupt_unregisterInterrupt) for Interrupt_unregisterInterrupt
    cs.o(i._CSComputeCLKFrequency) refers to cs.o(i.CS_clearInterruptFlag) for CS_clearInterruptFlag
    cs.o(i._CSComputeCLKFrequency) refers to cs.o(i.CS_getDCOFrequency) for CS_getDCOFrequency
    cs.o(i._CSComputeCLKFrequency) refers to cs.o(.data) for .data
    cs.o(i._CSGetHFXTFrequency) refers to cs.o(.data) for .data
    gpio.o(i.GPIO_clearInterruptFlag) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_disableInterrupt) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_enableInterrupt) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_getEnabledInterruptStatus) refers to gpio.o(i.GPIO_getInterruptStatus) for GPIO_getInterruptStatus
    gpio.o(i.GPIO_getEnabledInterruptStatus) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_getInputPinValue) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_getInterruptStatus) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_interruptEdgeSelect) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_registerInterrupt) refers to interrupt.o(i.Interrupt_registerInterrupt) for Interrupt_registerInterrupt
    gpio.o(i.GPIO_registerInterrupt) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    gpio.o(i.GPIO_registerInterrupt) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setAsInputPin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setAsInputPinWithPullDownResistor) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setAsInputPinWithPullUpResistor) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setAsOutputPin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setAsPeripheralModuleFunctionInputPin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setAsPeripheralModuleFunctionOutputPin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setDriveStrengthHigh) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setDriveStrengthLow) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setOutputHighOnPin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_setOutputLowOnPin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_toggleOutputOnPin) refers to gpio.o(.constdata) for .constdata
    gpio.o(i.GPIO_unregisterInterrupt) refers to interrupt.o(i.Interrupt_disableInterrupt) for Interrupt_disableInterrupt
    gpio.o(i.GPIO_unregisterInterrupt) refers to interrupt.o(i.Interrupt_unregisterInterrupt) for Interrupt_unregisterInterrupt
    gpio.o(i.GPIO_unregisterInterrupt) refers to gpio.o(.constdata) for .constdata
    i2c.o(i.I2C_registerInterrupt) refers to interrupt.o(i.Interrupt_registerInterrupt) for Interrupt_registerInterrupt
    i2c.o(i.I2C_registerInterrupt) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    i2c.o(i.I2C_unregisterInterrupt) refers to interrupt.o(i.Interrupt_disableInterrupt) for Interrupt_disableInterrupt
    i2c.o(i.I2C_unregisterInterrupt) refers to interrupt.o(i.Interrupt_unregisterInterrupt) for Interrupt_unregisterInterrupt
    interrupt.o(i.Interrupt_disableInterrupt) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_disableMaster) refers to cpu.o(.emb_text) for CPU_cpsid
    interrupt.o(i.Interrupt_enableInterrupt) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_enableMaster) refers to cpu.o(.emb_text) for CPU_cpsie
    interrupt.o(i.Interrupt_getPriority) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_getPriorityGrouping) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_getPriorityMask) refers to cpu.o(.emb_text) for CPU_basepriGet
    interrupt.o(i.Interrupt_isEnabled) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_pendInterrupt) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_registerInterrupt) refers to interrupt.o(vtable) for vtable
    interrupt.o(i.Interrupt_setPriority) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_setPriorityGrouping) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_setPriorityMask) refers to cpu.o(.emb_text) for CPU_basepriSet
    interrupt.o(i.Interrupt_unpendInterrupt) refers to interrupt.o(.constdata) for .constdata
    interrupt.o(i.Interrupt_unregisterInterrupt) refers to interrupt.o(i.IntDefaultHandler) for IntDefaultHandler
    interrupt.o(i.Interrupt_unregisterInterrupt) refers to interrupt.o(vtable) for vtable
    timer_a.o(i.Timer_A_configureContinuousMode) refers to timer_a.o(i.privateTimer_AProcessClockSourceDivider) for privateTimer_AProcessClockSourceDivider
    timer_a.o(i.Timer_A_configureUpDownMode) refers to timer_a.o(i.privateTimer_AProcessClockSourceDivider) for privateTimer_AProcessClockSourceDivider
    timer_a.o(i.Timer_A_configureUpMode) refers to timer_a.o(i.privateTimer_AProcessClockSourceDivider) for privateTimer_AProcessClockSourceDivider
    timer_a.o(i.Timer_A_generatePWM) refers to timer_a.o(i.privateTimer_AProcessClockSourceDivider) for privateTimer_AProcessClockSourceDivider
    timer_a.o(i.Timer_A_getCaptureCompareEnabledInterruptStatus) refers to timer_a.o(i.Timer_A_getCaptureCompareInterruptStatus) for Timer_A_getCaptureCompareInterruptStatus
    timer_a.o(i.Timer_A_registerInterrupt) refers to interrupt.o(i.Interrupt_registerInterrupt) for Interrupt_registerInterrupt
    timer_a.o(i.Timer_A_registerInterrupt) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    timer_a.o(i.Timer_A_unregisterInterrupt) refers to interrupt.o(i.Interrupt_disableInterrupt) for Interrupt_disableInterrupt
    timer_a.o(i.Timer_A_unregisterInterrupt) refers to interrupt.o(i.Interrupt_unregisterInterrupt) for Interrupt_unregisterInterrupt
    uart.o(i.UART_registerInterrupt) refers to interrupt.o(i.Interrupt_registerInterrupt) for Interrupt_registerInterrupt
    uart.o(i.UART_registerInterrupt) refers to interrupt.o(i.Interrupt_enableInterrupt) for Interrupt_enableInterrupt
    uart.o(i.UART_unregisterInterrupt) refers to interrupt.o(i.Interrupt_disableInterrupt) for Interrupt_disableInterrupt
    uart.o(i.UART_unregisterInterrupt) refers to interrupt.o(i.Interrupt_unregisterInterrupt) for Interrupt_unregisterInterrupt
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_msp432p401r_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_msp432p401r_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing system_msp432p401r.o(.rev16_text), (4 bytes).
    Removing system_msp432p401r.o(.revsh_text), (4 bytes).
    Removing system_msp432p401r.o(.rrx_text), (6 bytes).
    Removing system_msp432p401r.o(i.SystemCoreClockUpdate), (452 bytes).
    Removing system_msp432p401r.o(.data), (4 bytes).
    Removing startup_msp432p401r_uvision.o(HEAP), (0 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.ADC_Config), (152 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing key.o(.rrx_text), (6 bytes).
    Removing key4x4.o(.rev16_text), (4 bytes).
    Removing key4x4.o(.revsh_text), (4 bytes).
    Removing key4x4.o(.rrx_text), (6 bytes).
    Removing key4x4.o(i.KEY4x4_Init), (44 bytes).
    Removing key4x4.o(i.KEY4x4_Scan), (176 bytes).
    Removing key4x4.o(.data), (1 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing led.o(.rrx_text), (6 bytes).
    Removing led.o(i.LED_B_Off), (16 bytes).
    Removing led.o(i.LED_B_On), (16 bytes).
    Removing led.o(i.LED_C_On), (20 bytes).
    Removing led.o(i.LED_G_Off), (16 bytes).
    Removing led.o(i.LED_G_On), (16 bytes).
    Removing led.o(i.LED_P_On), (20 bytes).
    Removing led.o(i.LED_RED_Off), (16 bytes).
    Removing led.o(i.LED_RED_On), (16 bytes).
    Removing led.o(i.LED_R_Off), (16 bytes).
    Removing led.o(i.LED_R_On), (16 bytes).
    Removing led.o(i.LED_W_Off), (20 bytes).
    Removing led.o(i.LED_W_On), (20 bytes).
    Removing led.o(i.LED_W_Tog), (18 bytes).
    Removing led.o(i.LED_Y_On), (20 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_ColorTurn), (22 bytes).
    Removing oled.o(i.OLED_DisplayTurn), (44 bytes).
    Removing oled.o(i.OLED_Display_Off), (30 bytes).
    Removing oled.o(i.OLED_Display_On), (30 bytes).
    Removing oled.o(i.OLED_DrawBMP), (80 bytes).
    Removing oled.o(i.OLED_ShowChinese), (84 bytes).
    Removing oled.o(i.OLED_ShowString), (56 bytes).
    Removing tim32.o(.rev16_text), (4 bytes).
    Removing tim32.o(.revsh_text), (4 bytes).
    Removing tim32.o(.rrx_text), (6 bytes).
    Removing tim32.o(i.Tim32_0_Int_Init), (76 bytes).
    Removing tim32.o(i.Tim32_1_Int_Init), (76 bytes).
    Removing tima.o(.rev16_text), (4 bytes).
    Removing tima.o(.revsh_text), (4 bytes).
    Removing tima.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i._sys_exit), (2 bytes).
    Removing usart.o(i.uart2_init), (92 bytes).
    Removing sysinit.o(.rev16_text), (4 bytes).
    Removing sysinit.o(.revsh_text), (4 bytes).
    Removing sysinit.o(.rrx_text), (6 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing delay.o(.rrx_text), (6 bytes).
    Removing baudrate_calculate.o(.rev16_text), (4 bytes).
    Removing baudrate_calculate.o(.revsh_text), (4 bytes).
    Removing baudrate_calculate.o(.rrx_text), (6 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rrx_text), (6 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.rrx_text), (6 bytes).
    Removing globalvar.o(.rev16_text), (4 bytes).
    Removing globalvar.o(.revsh_text), (4 bytes).
    Removing globalvar.o(.rrx_text), (6 bytes).
    Removing globalvar.o(.data), (4 bytes).
    Removing globalvar.o(.data), (4 bytes).
    Removing pidcontrol.o(i.Positional_PID_Calc), (134 bytes).
    Removing pidcontrol.o(i.Positional_PID_Init), (14 bytes).
    Removing grayscale.o(.rev16_text), (4 bytes).
    Removing grayscale.o(.revsh_text), (4 bytes).
    Removing grayscale.o(.rrx_text), (6 bytes).
    Removing fsm.o(.rev16_text), (4 bytes).
    Removing fsm.o(.revsh_text), (4 bytes).
    Removing fsm.o(.rrx_text), (6 bytes).
    Removing adc14.o(.rev16_text), (4 bytes).
    Removing adc14.o(.revsh_text), (4 bytes).
    Removing adc14.o(.rrx_text), (6 bytes).
    Removing adc14.o(i.ADC14_clearInterruptFlag), (28 bytes).
    Removing adc14.o(i.ADC14_configureConversionMemory), (100 bytes).
    Removing adc14.o(i.ADC14_configureMultiSequenceMode), (124 bytes).
    Removing adc14.o(i.ADC14_configureSingleSampleMode), (60 bytes).
    Removing adc14.o(i.ADC14_disableComparatorWindow), (64 bytes).
    Removing adc14.o(i.ADC14_disableConversion), (16 bytes).
    Removing adc14.o(i.ADC14_disableInterrupt), (28 bytes).
    Removing adc14.o(i.ADC14_disableModule), (28 bytes).
    Removing adc14.o(i.ADC14_disableReferenceBurst), (28 bytes).
    Removing adc14.o(i.ADC14_disableSampleTimer), (28 bytes).
    Removing adc14.o(i.ADC14_enableComparatorWindow), (96 bytes).
    Removing adc14.o(i.ADC14_enableConversion), (40 bytes).
    Removing adc14.o(i.ADC14_enableInterrupt), (28 bytes).
    Removing adc14.o(i.ADC14_enableModule), (12 bytes).
    Removing adc14.o(i.ADC14_enableReferenceBurst), (28 bytes).
    Removing adc14.o(i.ADC14_enableSampleTimer), (40 bytes).
    Removing adc14.o(i.ADC14_getEnabledInterruptStatus), (28 bytes).
    Removing adc14.o(i.ADC14_getInterruptStatus), (16 bytes).
    Removing adc14.o(i.ADC14_getResolution), (16 bytes).
    Removing adc14.o(i.ADC14_getResult), (32 bytes).
    Removing adc14.o(i.ADC14_getResultArray), (92 bytes).
    Removing adc14.o(i.ADC14_initModule), (64 bytes).
    Removing adc14.o(i.ADC14_isBusy), (20 bytes).
    Removing adc14.o(i.ADC14_registerInterrupt), (20 bytes).
    Removing adc14.o(i.ADC14_setComparatorWindowValue), (76 bytes).
    Removing adc14.o(i.ADC14_setPowerMode), (48 bytes).
    Removing adc14.o(i.ADC14_setResolution), (20 bytes).
    Removing adc14.o(i.ADC14_setResultFormat), (44 bytes).
    Removing adc14.o(i.ADC14_setSampleHoldTime), (40 bytes).
    Removing adc14.o(i.ADC14_setSampleHoldTrigger), (44 bytes).
    Removing adc14.o(i.ADC14_toggleConversionTrigger), (32 bytes).
    Removing adc14.o(i.ADC14_unregisterInterrupt), (18 bytes).
    Removing adc14.o(i.ADCIsConversionRunning), (20 bytes).
    Removing adc14.o(i._getIndexForMemRegister), (342 bytes).
    Removing adc14.o(.constdata), (128 bytes).
    Removing cs.o(.rev16_text), (4 bytes).
    Removing cs.o(.revsh_text), (4 bytes).
    Removing cs.o(.rrx_text), (6 bytes).
    Removing cs.o(i.CS_disableClockRequest), (36 bytes).
    Removing cs.o(i.CS_disableDCOExternalResistor), (32 bytes).
    Removing cs.o(i.CS_disableFaultCounter), (40 bytes).
    Removing cs.o(i.CS_disableInterrupt), (36 bytes).
    Removing cs.o(i.CS_enableClockRequest), (36 bytes).
    Removing cs.o(i.CS_enableDCOExternalResistor), (28 bytes).
    Removing cs.o(i.CS_enableFaultCounter), (40 bytes).
    Removing cs.o(i.CS_enableInterrupt), (36 bytes).
    Removing cs.o(i.CS_getBCLK), (28 bytes).
    Removing cs.o(i.CS_getEnabledInterruptStatus), (20 bytes).
    Removing cs.o(i.CS_getHSMCLK), (24 bytes).
    Removing cs.o(i.CS_getInterruptStatus), (12 bytes).
    Removing cs.o(i.CS_initClockSignal), (232 bytes).
    Removing cs.o(i.CS_registerInterrupt), (20 bytes).
    Removing cs.o(i.CS_resetFaultCounter), (40 bytes).
    Removing cs.o(i.CS_setDCOCenteredFrequency), (36 bytes).
    Removing cs.o(i.CS_setDCOExternalResistorCalibration), (100 bytes).
    Removing cs.o(i.CS_setDCOFrequency), (300 bytes).
    Removing cs.o(i.CS_setReferenceOscillatorFrequency), (32 bytes).
    Removing cs.o(i.CS_startFaultCounter), (52 bytes).
    Removing cs.o(i.CS_tuneDCOFrequency), (52 bytes).
    Removing cs.o(i.CS_unregisterInterrupt), (18 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing gpio.o(i.GPIO_disableInterrupt), (20 bytes).
    Removing gpio.o(i.GPIO_registerInterrupt), (28 bytes).
    Removing gpio.o(i.GPIO_setAsInputPinWithPullDownResistor), (44 bytes).
    Removing gpio.o(i.GPIO_setAsInputPinWithPullUpResistor), (44 bytes).
    Removing gpio.o(i.GPIO_setAsPeripheralModuleFunctionInputPin), (60 bytes).
    Removing gpio.o(i.GPIO_setAsPeripheralModuleFunctionOutputPin), (60 bytes).
    Removing gpio.o(i.GPIO_setDriveStrengthHigh), (20 bytes).
    Removing gpio.o(i.GPIO_setDriveStrengthLow), (20 bytes).
    Removing gpio.o(i.GPIO_toggleOutputOnPin), (20 bytes).
    Removing gpio.o(i.GPIO_unregisterInterrupt), (28 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.I2C_clearInterruptFlag), (8 bytes).
    Removing i2c.o(i.I2C_disableInterrupt), (8 bytes).
    Removing i2c.o(i.I2C_disableModule), (12 bytes).
    Removing i2c.o(i.I2C_disableMultiMasterMode), (18 bytes).
    Removing i2c.o(i.I2C_enableInterrupt), (8 bytes).
    Removing i2c.o(i.I2C_enableModule), (12 bytes).
    Removing i2c.o(i.I2C_enableMultiMasterMode), (16 bytes).
    Removing i2c.o(i.I2C_getEnabledInterruptStatus), (8 bytes).
    Removing i2c.o(i.I2C_getInterruptStatus), (6 bytes).
    Removing i2c.o(i.I2C_getMode), (8 bytes).
    Removing i2c.o(i.I2C_getReceiveBufferAddressForDMA), (4 bytes).
    Removing i2c.o(i.I2C_getTransmitBufferAddressForDMA), (4 bytes).
    Removing i2c.o(i.I2C_initMaster), (58 bytes).
    Removing i2c.o(i.I2C_initSlave), (32 bytes).
    Removing i2c.o(i.I2C_isBusBusy), (12 bytes).
    Removing i2c.o(i.I2C_masterIsStartSent), (16 bytes).
    Removing i2c.o(i.I2C_masterIsStopSent), (10 bytes).
    Removing i2c.o(i.I2C_masterReceiveMultiByteFinish), (32 bytes).
    Removing i2c.o(i.I2C_masterReceiveMultiByteFinishWithTimeout), (50 bytes).
    Removing i2c.o(i.I2C_masterReceiveMultiByteNext), (6 bytes).
    Removing i2c.o(i.I2C_masterReceiveMultiByteStop), (12 bytes).
    Removing i2c.o(i.I2C_masterReceiveSingle), (24 bytes).
    Removing i2c.o(i.I2C_masterReceiveSingleByte), (56 bytes).
    Removing i2c.o(i.I2C_masterReceiveStart), (14 bytes).
    Removing i2c.o(i.I2C_masterSendMultiByteFinishWithTimeout), (64 bytes).
    Removing i2c.o(i.I2C_masterSendMultiByteNext), (22 bytes).
    Removing i2c.o(i.I2C_masterSendMultiByteNextWithTimeout), (36 bytes).
    Removing i2c.o(i.I2C_masterSendMultiByteStartWithTimeout), (60 bytes).
    Removing i2c.o(i.I2C_masterSendMultiByteStop), (24 bytes).
    Removing i2c.o(i.I2C_masterSendMultiByteStopWithTimeout), (36 bytes).
    Removing i2c.o(i.I2C_masterSendSingleByte), (74 bytes).
    Removing i2c.o(i.I2C_masterSendSingleByteWithTimeout), (80 bytes).
    Removing i2c.o(i.I2C_masterSendStart), (12 bytes).
    Removing i2c.o(i.I2C_registerInterrupt), (80 bytes).
    Removing i2c.o(i.I2C_setMode), (12 bytes).
    Removing i2c.o(i.I2C_setSlaveAddress), (4 bytes).
    Removing i2c.o(i.I2C_setTimeout), (28 bytes).
    Removing i2c.o(i.I2C_slaveGetData), (6 bytes).
    Removing i2c.o(i.I2C_slavePutData), (4 bytes).
    Removing i2c.o(i.I2C_slaveSendNAK), (12 bytes).
    Removing i2c.o(i.I2C_unregisterInterrupt), (80 bytes).
    Removing interrupt.o(.rev16_text), (4 bytes).
    Removing interrupt.o(.revsh_text), (4 bytes).
    Removing interrupt.o(.rrx_text), (6 bytes).
    Removing interrupt.o(i.IntDefaultHandler), (2 bytes).
    Removing interrupt.o(i.Interrupt_disableInterrupt), (92 bytes).
    Removing interrupt.o(i.Interrupt_disableMaster), (14 bytes).
    Removing interrupt.o(i.Interrupt_disableSleepOnIsrExit), (16 bytes).
    Removing interrupt.o(i.Interrupt_enableMaster), (14 bytes).
    Removing interrupt.o(i.Interrupt_enableSleepOnIsrExit), (16 bytes).
    Removing interrupt.o(i.Interrupt_getPriority), (24 bytes).
    Removing interrupt.o(i.Interrupt_getPriorityGrouping), (36 bytes).
    Removing interrupt.o(i.Interrupt_getPriorityMask), (10 bytes).
    Removing interrupt.o(i.Interrupt_getVectorTableAddress), (12 bytes).
    Removing interrupt.o(i.Interrupt_isEnabled), (100 bytes).
    Removing interrupt.o(i.Interrupt_pendInterrupt), (76 bytes).
    Removing interrupt.o(i.Interrupt_registerInterrupt), (48 bytes).
    Removing interrupt.o(i.Interrupt_setPriorityGrouping), (40 bytes).
    Removing interrupt.o(i.Interrupt_setPriorityMask), (4 bytes).
    Removing interrupt.o(i.Interrupt_setVectorTableAddress), (12 bytes).
    Removing interrupt.o(i.Interrupt_unpendInterrupt), (64 bytes).
    Removing interrupt.o(i.Interrupt_unregisterInterrupt), (20 bytes).
    Removing interrupt.o(vtable), (232 bytes).
    Removing timer_a.o(.rev16_text), (4 bytes).
    Removing timer_a.o(.revsh_text), (4 bytes).
    Removing timer_a.o(.rrx_text), (6 bytes).
    Removing timer_a.o(i.Timer_A_clearCaptureCompareInterrupt), (26 bytes).
    Removing timer_a.o(i.Timer_A_clearInterruptFlag), (12 bytes).
    Removing timer_a.o(i.Timer_A_clearTimer), (12 bytes).
    Removing timer_a.o(i.Timer_A_configureContinuousMode), (40 bytes).
    Removing timer_a.o(i.Timer_A_configureUpDownMode), (70 bytes).
    Removing timer_a.o(i.Timer_A_configureUpMode), (70 bytes).
    Removing timer_a.o(i.Timer_A_disableCaptureCompareInterrupt), (26 bytes).
    Removing timer_a.o(i.Timer_A_disableInterrupt), (12 bytes).
    Removing timer_a.o(i.Timer_A_enableCaptureCompareInterrupt), (26 bytes).
    Removing timer_a.o(i.Timer_A_enableInterrupt), (12 bytes).
    Removing timer_a.o(i.Timer_A_generatePWM), (76 bytes).
    Removing timer_a.o(i.Timer_A_getCaptureCompareEnabledInterruptStatus), (34 bytes).
    Removing timer_a.o(i.Timer_A_getCaptureCompareInterruptStatus), (18 bytes).
    Removing timer_a.o(i.Timer_A_getCounterValue), (28 bytes).
    Removing timer_a.o(i.Timer_A_getEnabledInterruptStatus), (18 bytes).
    Removing timer_a.o(i.Timer_A_getInterruptStatus), (8 bytes).
    Removing timer_a.o(i.Timer_A_getOutputForOutputModeOutBitValue), (30 bytes).
    Removing timer_a.o(i.Timer_A_getSynchronizedCaptureCompareInput), (22 bytes).
    Removing timer_a.o(i.Timer_A_initCapture), (52 bytes).
    Removing timer_a.o(i.Timer_A_initCompare), (38 bytes).
    Removing timer_a.o(i.Timer_A_registerInterrupt), (164 bytes).
    Removing timer_a.o(i.Timer_A_setCompareValue), (16 bytes).
    Removing timer_a.o(i.Timer_A_setOutputForOutputModeOutBitValue), (24 bytes).
    Removing timer_a.o(i.Timer_A_startCounter), (8 bytes).
    Removing timer_a.o(i.Timer_A_stopTimer), (10 bytes).
    Removing timer_a.o(i.Timer_A_unregisterInterrupt), (140 bytes).
    Removing timer_a.o(i.privateTimer_AProcessClockSourceDivider), (180 bytes).
    Removing uart.o(.rev16_text), (4 bytes).
    Removing uart.o(.revsh_text), (4 bytes).
    Removing uart.o(.rrx_text), (6 bytes).
    Removing uart.o(i.UART_disableInterrupt), (22 bytes).
    Removing uart.o(i.UART_disableModule), (12 bytes).
    Removing uart.o(i.UART_enableModule), (12 bytes).
    Removing uart.o(i.UART_getEnabledInterruptStatus), (38 bytes).
    Removing uart.o(i.UART_getInterruptStatus), (6 bytes).
    Removing uart.o(i.UART_getReceiveBufferAddressForDMA), (4 bytes).
    Removing uart.o(i.UART_getTransmitBufferAddressForDMA), (4 bytes).
    Removing uart.o(i.UART_queryStatusFlags), (6 bytes).
    Removing uart.o(i.UART_registerInterrupt), (80 bytes).
    Removing uart.o(i.UART_resetDormant), (12 bytes).
    Removing uart.o(i.UART_selectDeglitchTime), (12 bytes).
    Removing uart.o(i.UART_setDormant), (12 bytes).
    Removing uart.o(i.UART_transmitAddress), (14 bytes).
    Removing uart.o(i.UART_transmitBreak), (44 bytes).
    Removing uart.o(i.UART_unregisterInterrupt), (80 bytes).
    Removing cpu.o(.rev16_text), (4 bytes).
    Removing cpu.o(.revsh_text), (4 bytes).
    Removing cpu.o(.rrx_text), (6 bytes).
    Removing cpu.o(.emb_text), (38 bytes).
    Removing sysctl_a.o(.rev16_text), (4 bytes).
    Removing sysctl_a.o(.revsh_text), (4 bytes).
    Removing sysctl_a.o(.rrx_text), (6 bytes).
    Removing sysctl_a.o(i.SysCtl_A_disableGlitchFilter), (16 bytes).
    Removing sysctl_a.o(i.SysCtl_A_disablePeripheralAtCPUHalt), (16 bytes).
    Removing sysctl_a.o(i.SysCtl_A_disableSRAM), (96 bytes).
    Removing sysctl_a.o(i.SysCtl_A_disableSRAMRetention), (272 bytes).
    Removing sysctl_a.o(i.SysCtl_A_enableGlitchFilter), (16 bytes).
    Removing sysctl_a.o(i.SysCtl_A_enablePeripheralAtCPUHalt), (16 bytes).
    Removing sysctl_a.o(i.SysCtl_A_enableSRAM), (96 bytes).
    Removing sysctl_a.o(i.SysCtl_A_enableSRAMRetention), (284 bytes).
    Removing sysctl_a.o(i.SysCtl_A_getFlashSize), (12 bytes).
    Removing sysctl_a.o(i.SysCtl_A_getInfoFlashSize), (12 bytes).
    Removing sysctl_a.o(i.SysCtl_A_getSRAMSize), (12 bytes).
    Removing sysctl_a.o(i.SysCtl_A_getTempCalibrationConstant), (12 bytes).
    Removing sysctl_a.o(i.SysCtl_A_rebootDevice), (16 bytes).
    Removing sysctl_a.o(i.SysCtl_A_setWDTPasswordViolationResetType), (28 bytes).
    Removing sysctl_a.o(i.SysCtl_A_setWDTTimeoutResetType), (28 bytes).
    Removing ddiv.o(.text), (222 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

305 unused section(s) (total 10361 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    RESET                                    0x00000000   Section      324  startup_msp432p401r_uvision.o(RESET)
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ..\..\CODE\FSM.c                         0x00000000   Number         0  fsm.o ABSOLUTE
    ..\..\CODE\PIDcontrol.c                  0x00000000   Number         0  pidcontrol.o ABSOLUTE
    ..\..\CODE\encoder.c                     0x00000000   Number         0  encoder.o ABSOLUTE
    ..\..\CODE\globalvar.c                   0x00000000   Number         0  globalvar.o ABSOLUTE
    ..\..\CODE\grayscale.c                   0x00000000   Number         0  grayscale.o ABSOLUTE
    ..\..\CODE\motor.c                       0x00000000   Number         0  motor.o ABSOLUTE
    ..\..\hardware\adc.c                     0x00000000   Number         0  adc.o ABSOLUTE
    ..\..\hardware\key.c                     0x00000000   Number         0  key.o ABSOLUTE
    ..\..\hardware\key4x4.c                  0x00000000   Number         0  key4x4.o ABSOLUTE
    ..\..\hardware\led.c                     0x00000000   Number         0  led.o ABSOLUTE
    ..\..\hardware\oled.c                    0x00000000   Number         0  oled.o ABSOLUTE
    ..\..\hardware\tim32.c                   0x00000000   Number         0  tim32.o ABSOLUTE
    ..\..\hardware\timA.c                    0x00000000   Number         0  tima.o ABSOLUTE
    ..\..\main.c                             0x00000000   Number         0  main.o ABSOLUTE
    ..\..\sys\baudrate_calculate.c           0x00000000   Number         0  baudrate_calculate.o ABSOLUTE
    ..\..\sys\delay.c                        0x00000000   Number         0  delay.o ABSOLUTE
    ..\..\sys\sysinit.c                      0x00000000   Number         0  sysinit.o ABSOLUTE
    ..\..\sys\usart.c                        0x00000000   Number         0  usart.o ABSOLUTE
    ..\..\system_msp432p401r.c               0x00000000   Number         0  system_msp432p401r.o ABSOLUTE
    ..\\..\\CODE\\FSM.c                      0x00000000   Number         0  fsm.o ABSOLUTE
    ..\\..\\CODE\\encoder.c                  0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\..\\CODE\\globalvar.c                0x00000000   Number         0  globalvar.o ABSOLUTE
    ..\\..\\CODE\\grayscale.c                0x00000000   Number         0  grayscale.o ABSOLUTE
    ..\\..\\CODE\\motor.c                    0x00000000   Number         0  motor.o ABSOLUTE
    ..\\..\\hardware\\adc.c                  0x00000000   Number         0  adc.o ABSOLUTE
    ..\\..\\hardware\\key.c                  0x00000000   Number         0  key.o ABSOLUTE
    ..\\..\\hardware\\key4x4.c               0x00000000   Number         0  key4x4.o ABSOLUTE
    ..\\..\\hardware\\led.c                  0x00000000   Number         0  led.o ABSOLUTE
    ..\\..\\hardware\\oled.c                 0x00000000   Number         0  oled.o ABSOLUTE
    ..\\..\\hardware\\tim32.c                0x00000000   Number         0  tim32.o ABSOLUTE
    ..\\..\\hardware\\timA.c                 0x00000000   Number         0  tima.o ABSOLUTE
    ..\\..\\main.c                           0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\sys\\baudrate_calculate.c        0x00000000   Number         0  baudrate_calculate.o ABSOLUTE
    ..\\..\\sys\\delay.c                     0x00000000   Number         0  delay.o ABSOLUTE
    ..\\..\\sys\\sysinit.c                   0x00000000   Number         0  sysinit.o ABSOLUTE
    ..\\..\\sys\\usart.c                     0x00000000   Number         0  usart.o ABSOLUTE
    ..\\..\\system_msp432p401r.c             0x00000000   Number         0  system_msp432p401r.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/adc14.c 0x00000000   Number         0  adc14.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/adc14.c 0x00000000   Number         0  adc14.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/cpu.c 0x00000000   Number         0  cpu.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/cpu.c 0x00000000   Number         0  cpu.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/cs.c 0x00000000   Number         0  cs.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/cs.c 0x00000000   Number         0  cs.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/gpio.c 0x00000000   Number         0  gpio.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/gpio.c 0x00000000   Number         0  gpio.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/i2c.c 0x00000000   Number         0  i2c.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/i2c.c 0x00000000   Number         0  i2c.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/interrupt.c 0x00000000   Number         0  interrupt.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/interrupt.c 0x00000000   Number         0  interrupt.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/sysctl_a.c 0x00000000   Number         0  sysctl_a.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/sysctl_a.c 0x00000000   Number         0  sysctl_a.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/timer_a.c 0x00000000   Number         0  timer_a.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/timer_a.c 0x00000000   Number         0  timer_a.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/uart.c 0x00000000   Number         0  uart.o ABSOLUTE
    source/ti/devices/msp432p4xx/driverlib/uart.c 0x00000000   Number         0  uart.o ABSOLUTE
    startup_msp432p401r_uvision.s            0x00000000   Number         0  startup_msp432p401r_uvision.o ABSOLUTE
    .ARM.Collect$$$$00000000                 0x00000144   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x00000144   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x00000148   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x0000014c   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x0000014c   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x0000014c   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x00000154   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x00000154   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x00000154   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x00000154   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x00000158   Section       36  startup_msp432p401r_uvision.o(.text)
    $v0                                      0x00000158   Number         0  startup_msp432p401r_uvision.o(.text)
    .text                                    0x0000017c   Section        0  memcpya.o(.text)
    .text                                    0x000001a0   Section        0  dadd.o(.text)
    .text                                    0x000002ee   Section        0  dmul.o(.text)
    .text                                    0x000003d2   Section        0  dflti.o(.text)
    .text                                    0x000003f4   Section        0  dfixi.o(.text)
    .text                                    0x00000432   Section        0  uidiv.o(.text)
    .text                                    0x0000045e   Section        0  llshl.o(.text)
    .text                                    0x0000047c   Section        0  llushr.o(.text)
    .text                                    0x0000049c   Section        0  llsshr.o(.text)
    .text                                    0x000004c0   Section        0  depilogue.o(.text)
    .text                                    0x000004c0   Section        0  iusefp.o(.text)
    .text                                    0x0000057c   Section       36  init.o(.text)
    i.ADC14_IRQHandler                       0x000005a0   Section        0  adc.o(i.ADC14_IRQHandler)
    i.ADC14_getMultiSequenceResult           0x000005fc   Section        0  adc14.o(i.ADC14_getMultiSequenceResult)
    i.BEEP_Init                              0x00000628   Section        0  led.o(i.BEEP_Init)
    i.BEEP_Off                               0x00000650   Section        0  led.o(i.BEEP_Off)
    i.BEEP_On                                0x0000065c   Section        0  led.o(i.BEEP_On)
    i.CS_clearInterruptFlag                  0x00000668   Section        0  cs.o(i.CS_clearInterruptFlag)
    i.CS_getACLK                             0x0000068c   Section        0  cs.o(i.CS_getACLK)
    i.CS_getDCOFrequency                     0x000006a4   Section        0  cs.o(i.CS_getDCOFrequency)
    i.CS_getMCLK                             0x000007b0   Section        0  cs.o(i.CS_getMCLK)
    i.CS_getSMCLK                            0x000007c8   Section        0  cs.o(i.CS_getSMCLK)
    i.CS_setExternalClockSourceFrequency     0x000007e0   Section        0  cs.o(i.CS_setExternalClockSourceFrequency)
    i.CS_startHFXT                           0x000007ec   Section        0  cs.o(i.CS_startHFXT)
    i.CS_startHFXTWithTimeout                0x000007f4   Section        0  cs.o(i.CS_startHFXTWithTimeout)
    i.CS_startLFXT                           0x00000894   Section        0  cs.o(i.CS_startLFXT)
    i.CS_startLFXTWithTimeout                0x0000089c   Section        0  cs.o(i.CS_startLFXTWithTimeout)
    i.EUSCIA0_IRQHandler                     0x0000093c   Section        0  main.o(i.EUSCIA0_IRQHandler)
    i.FSM                                    0x0000095c   Section        0  fsm.o(i.FSM)
    i.GPIO_clearInterruptFlag                0x00000bd0   Section        0  gpio.o(i.GPIO_clearInterruptFlag)
    i.GPIO_enableInterrupt                   0x00000be4   Section        0  gpio.o(i.GPIO_enableInterrupt)
    i.GPIO_getEnabledInterruptStatus         0x00000bf8   Section        0  gpio.o(i.GPIO_getEnabledInterruptStatus)
    i.GPIO_getInputPinValue                  0x00000c30   Section        0  gpio.o(i.GPIO_getInputPinValue)
    i.GPIO_getInterruptStatus                0x00000c44   Section        0  gpio.o(i.GPIO_getInterruptStatus)
    i.GPIO_interruptEdgeSelect               0x00000c54   Section        0  gpio.o(i.GPIO_interruptEdgeSelect)
    i.GPIO_setAsInputPin                     0x00000c70   Section        0  gpio.o(i.GPIO_setAsInputPin)
    i.GPIO_setAsOutputPin                    0x00000c94   Section        0  gpio.o(i.GPIO_setAsOutputPin)
    i.GPIO_setOutputHighOnPin                0x00000cb4   Section        0  gpio.o(i.GPIO_setOutputHighOnPin)
    i.GPIO_setOutputLowOnPin                 0x00000cc8   Section        0  gpio.o(i.GPIO_setOutputLowOnPin)
    i.Grayscale_Init                         0x00000cdc   Section        0  grayscale.o(i.Grayscale_Init)
    i.Grayscale_Judge                        0x00000d24   Section        0  grayscale.o(i.Grayscale_Judge)
    i.Grayscale_scan                         0x00000fac   Section        0  grayscale.o(i.Grayscale_scan)
    i.I2C_Configuration                      0x0000102c   Section        0  oled.o(i.I2C_Configuration)
    i.I2C_masterSendMultiByteFinish          0x00001084   Section        0  i2c.o(i.I2C_masterSendMultiByteFinish)
    i.I2C_masterSendMultiByteStart           0x000010b8   Section        0  i2c.o(i.I2C_masterSendMultiByteStart)
    i.Incremental_PID_Calc                   0x000010ec   Section        0  pidcontrol.o(i.Incremental_PID_Calc)
    i.Incremental_PID_Init                   0x0000115e   Section        0  pidcontrol.o(i.Incremental_PID_Init)
    i.Inner_Ring                             0x00001168   Section        0  grayscale.o(i.Inner_Ring)
    i.Interrupt_enableInterrupt              0x00001254   Section        0  interrupt.o(i.Interrupt_enableInterrupt)
    i.Interrupt_setPriority                  0x000012b0   Section        0  interrupt.o(i.Interrupt_setPriority)
    i.KEY_Init                               0x000012d4   Section        0  key.o(i.KEY_Init)
    i.KEY_Scan                               0x00001334   Section        0  key.o(i.KEY_Scan)
    i.LED_B_Tog                              0x00001388   Section        0  led.o(i.LED_B_Tog)
    i.LED_G_Tog                              0x0000139c   Section        0  led.o(i.LED_G_Tog)
    i.LED_Init                               0x000013b0   Section        0  led.o(i.LED_Init)
    i.LED_RED_Tog                            0x000013e8   Section        0  led.o(i.LED_RED_Tog)
    i.LED_R_Tog                              0x000013fc   Section        0  led.o(i.LED_R_Tog)
    i.Motor_Init                             0x00001410   Section        0  motor.o(i.Motor_Init)
    i.Motor_PID                              0x00001430   Section        0  motor.o(i.Motor_PID)
    i.Motor_Set                              0x000014ac   Section        0  motor.o(i.Motor_Set)
    i.OLED_Clear                             0x00001534   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x00001570   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Pos                           0x0000165e   Section        0  oled.o(i.OLED_Set_Pos)
    i.OLED_ShowChar                          0x00001688   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowNum                           0x00001710   Section        0  oled.o(i.OLED_ShowNum)
    i.OLED_WR_Byte                           0x0000178c   Section        0  oled.o(i.OLED_WR_Byte)
    i.Outer_Ring                             0x000017ac   Section        0  grayscale.o(i.Outer_Ring)
    i.PORT1_IRQHandler                       0x00001890   Section        0  key.o(i.PORT1_IRQHandler)
    i.PORT2_IRQHandler                       0x000018c8   Section        0  encoder.o(i.PORT2_IRQHandler)
    i.PORT4_IRQHandler                       0x00001930   Section        0  encoder.o(i.PORT4_IRQHandler)
    i.PORT5_IRQHandler                       0x00001998   Section        0  encoder.o(i.PORT5_IRQHandler)
    i.PORT6_IRQHandler                       0x00001a00   Section        0  encoder.o(i.PORT6_IRQHandler)
    i.SW_Encoder_Clear                       0x00001a68   Section        0  encoder.o(i.SW_Encoder_Clear)
    i.SW_Encoder_Init                        0x00001a78   Section        0  encoder.o(i.SW_Encoder_Init)
    i.SW_Encoder_Read                        0x00001b7c   Section        0  encoder.o(i.SW_Encoder_Read)
    i.SysCtl_A_disableNMISource              0x00001b9c   Section        0  sysctl_a.o(i.SysCtl_A_disableNMISource)
    i.SysCtl_A_enableNMISource               0x00001bac   Section        0  sysctl_a.o(i.SysCtl_A_enableNMISource)
    i.SysCtl_A_getNMISourceStatus            0x00001bbc   Section        0  sysctl_a.o(i.SysCtl_A_getNMISourceStatus)
    i.SysCtl_A_getTLVInfo                    0x00001bcc   Section        0  sysctl_a.o(i.SysCtl_A_getTLVInfo)
    i.SysInit                                0x00001c14   Section        0  sysinit.o(i.SysInit)
    i.SystemInit                             0x00001ca0   Section        0  system_msp432p401r.o(i.SystemInit)
    i.T32_INT1_IRQHandler                    0x00001d00   Section        0  tim32.o(i.T32_INT1_IRQHandler)
    i.T32_INT2_IRQHandler                    0x00001d14   Section        0  tim32.o(i.T32_INT2_IRQHandler)
    i.TA1_0_IRQHandler                       0x00001d28   Section        0  main.o(i.TA1_0_IRQHandler)
    i.TA2_0_IRQHandler                       0x00001df4   Section        0  main.o(i.TA2_0_IRQHandler)
    i.TA3_N_IRQHandler                       0x00001f00   Section        0  tima.o(i.TA3_N_IRQHandler)
    i.TimA0_PWM_Init                         0x00001fd4   Section        0  tima.o(i.TimA0_PWM_Init)
    i.TimA1_Int_Init                         0x00002034   Section        0  tima.o(i.TimA1_Int_Init)
    i.TimA2_Int_Init                         0x0000209c   Section        0  tima.o(i.TimA2_Int_Init)
    i.TimA3_Cap_Init                         0x000020f0   Section        0  tima.o(i.TimA3_Cap_Init)
    i.Timer_A_getCaptureCompareCount         0x00002174   Section        0  timer_a.o(i.Timer_A_getCaptureCompareCount)
    i.UART_clearInterruptFlag                0x00002184   Section        0  uart.o(i.UART_clearInterruptFlag)
    i.UART_enableInterrupt                   0x0000218c   Section        0  uart.o(i.UART_enableInterrupt)
    i.UART_initModule                        0x000021a2   Section        0  uart.o(i.UART_initModule)
    i.UART_receiveData                       0x00002234   Section        0  uart.o(i.UART_receiveData)
    i.UART_transmitData                      0x0000224c   Section        0  uart.o(i.UART_transmitData)
    i._CSComputeCLKFrequency                 0x00002264   Section        0  cs.o(i._CSComputeCLKFrequency)
    _CSComputeCLKFrequency                   0x00002265   Thumb Code   176  cs.o(i._CSComputeCLKFrequency)
    i._CSGetHFXTFrequency                    0x00002324   Section        0  cs.o(i._CSGetHFXTFrequency)
    _CSGetHFXTFrequency                      0x00002325   Thumb Code   106  cs.o(i._CSGetHFXTFrequency)
    i.__0printf$1                            0x000023bc   Section        0  printf1.o(i.__0printf$1)
    i.__scatterload_copy                     0x000023dc   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x000023ea   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x000023ec   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._printf_core                           0x000023fc   Section        0  printf1.o(i._printf_core)
    _printf_core                             0x000023fd   Thumb Code   336  printf1.o(i._printf_core)
    i.bitPosition                            0x00002550   Section        0  baudrate_calculate.o(i.bitPosition)
    i.delay_init                             0x00002560   Section        0  delay.o(i.delay_init)
    i.delay_ms                               0x00002588   Section        0  delay.o(i.delay_ms)
    i.delay_us                               0x000025a0   Section        0  delay.o(i.delay_us)
    i.encoder_to_speed                       0x000025d4   Section        0  encoder.o(i.encoder_to_speed)
    i.eusci_calcBaudDividers                 0x0000261c   Section        0  baudrate_calculate.o(i.eusci_calcBaudDividers)
    i.fputc                                  0x00002748   Section        0  usart.o(i.fputc)
    i.key_delay                              0x0000275c   Section        0  key.o(i.key_delay)
    key_delay                                0x0000275d   Thumb Code    42  key.o(i.key_delay)
    i.main                                   0x00002788   Section        0  main.o(i.main)
    i.myabs                                  0x0000288c   Section        0  globalvar.o(i.myabs)
    i.oled_pow                               0x00002894   Section        0  oled.o(i.oled_pow)
    i.system_init                            0x000028a4   Section        0  main.o(i.system_init)
    i.uart_init                              0x00002a1c   Section        0  usart.o(i.uart_init)
    i.ult_start                              0x00002a78   Section        0  tima.o(i.ult_start)
    .constdata                               0x00002a94   Section     2336  oled.o(.constdata)
    .constdata                               0x000033b4   Section       64  tima.o(.constdata)
    .constdata                               0x000033f4   Section       80  usart.o(.constdata)
    .constdata                               0x00003444   Section       76  gpio.o(.constdata)
    GPIO_PORT_TO_INT                         0x00003444   Data          28  gpio.o(.constdata)
    GPIO_PORT_TO_BASE                        0x00003460   Data          48  gpio.o(.constdata)
    .constdata                               0x00003490   Section      144  interrupt.o(.constdata)
    g_pulEnRegs                              0x00003490   Data           8  interrupt.o(.constdata)
    g_pulDisRegs                             0x00003498   Data           8  interrupt.o(.constdata)
    g_pulPendRegs                            0x000034a0   Data           8  interrupt.o(.constdata)
    g_pulUnpendRegs                          0x000034a8   Data           8  interrupt.o(.constdata)
    g_pulPriority                            0x000034b0   Data          32  interrupt.o(.constdata)
    g_pulRegs                                0x000034d0   Data          80  interrupt.o(.constdata)
    .data                                    0x20000000   Section        4  main.o(.data)
    .data                                    0x20000004   Section        4  adc.o(.data)
    resultsBuffer                            0x20000004   Data           4  adc.o(.data)
    .data                                    0x20000008   Section        1  key.o(.data)
    key_up                                   0x20000008   Data           1  key.o(.data)
    .data                                    0x2000000c   Section       12  tima.o(.data)
    .data                                    0x20000018   Section        4  usart.o(.data)
    .data                                    0x2000001c   Section        1  delay.o(.data)
    fac_us                                   0x2000001c   Data           1  delay.o(.data)
    .data                                    0x20000020   Section        8  encoder.o(.data)
    .data                                    0x20000028   Section        4  globalvar.o(.data)
    .data                                    0x2000002c   Section        4  globalvar.o(.data)
    .data                                    0x20000030   Section        4  globalvar.o(.data)
    .data                                    0x20000034   Section        4  globalvar.o(.data)
    .data                                    0x20000038   Section        4  globalvar.o(.data)
    .data                                    0x2000003c   Section        4  globalvar.o(.data)
    .data                                    0x20000040   Section        4  globalvar.o(.data)
    .data                                    0x20000044   Section        4  globalvar.o(.data)
    .data                                    0x20000048   Section        4  globalvar.o(.data)
    .data                                    0x2000004c   Section        4  globalvar.o(.data)
    .data                                    0x20000050   Section        4  globalvar.o(.data)
    .data                                    0x20000054   Section        4  globalvar.o(.data)
    .data                                    0x20000058   Section        4  globalvar.o(.data)
    .data                                    0x2000005c   Section        4  globalvar.o(.data)
    .data                                    0x20000060   Section        4  globalvar.o(.data)
    .data                                    0x20000064   Section        4  globalvar.o(.data)
    .data                                    0x20000068   Section        4  globalvar.o(.data)
    .data                                    0x2000006c   Section        4  globalvar.o(.data)
    .data                                    0x20000070   Section        4  globalvar.o(.data)
    .data                                    0x20000074   Section        4  globalvar.o(.data)
    .data                                    0x20000078   Section        4  globalvar.o(.data)
    .data                                    0x2000007c   Section        4  globalvar.o(.data)
    .data                                    0x20000080   Section        4  globalvar.o(.data)
    .data                                    0x20000084   Section        4  globalvar.o(.data)
    .data                                    0x20000088   Section       32  grayscale.o(.data)
    .data                                    0x200000a8   Section        2  fsm.o(.data)
    .data                                    0x200000ac   Section        8  cs.o(.data)
    hfxtFreq                                 0x200000ac   Data           4  cs.o(.data)
    lfxtFreq                                 0x200000b0   Data           4  cs.o(.data)
    .bss                                     0x200000b4   Section       40  globalvar.o(.bss)
    .bss                                     0x200000dc   Section       40  globalvar.o(.bss)
    STACK                                    0x20000108   Section      512  startup_msp432p401r_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_msp432p401r_uvision.o(RESET)
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x00000144   Data           0  startup_msp432p401r_uvision.o(RESET)
    __Vectors_Size                           0x00000144   Number         0  startup_msp432p401r_uvision.o ABSOLUTE
    __main                                   0x00000145   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x00000145   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x00000149   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x0000014d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x0000014d   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x0000014d   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x0000014d   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x00000155   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x00000155   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x00000159   Thumb Code     8  startup_msp432p401r_uvision.o(.text)
    NMI_Handler                              0x00000161   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    HardFault_Handler                        0x00000163   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    MemManage_Handler                        0x00000165   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    BusFault_Handler                         0x00000167   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    UsageFault_Handler                       0x00000169   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    SVC_Handler                              0x0000016b   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    DebugMon_Handler                         0x0000016d   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    PendSV_Handler                           0x0000016f   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    SysTick_Handler                          0x00000171   Thumb Code     2  startup_msp432p401r_uvision.o(.text)
    AES256_IRQHandler                        0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    COMP_E0_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    COMP_E1_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    CS_IRQHandler                            0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    DMA_ERR_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    DMA_INT0_IRQHandler                      0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    DMA_INT1_IRQHandler                      0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    DMA_INT2_IRQHandler                      0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    DMA_INT3_IRQHandler                      0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    EUSCIA1_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    EUSCIA2_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    EUSCIA3_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    EUSCIB0_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    EUSCIB1_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    EUSCIB2_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    EUSCIB3_IRQHandler                       0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    FLCTL_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    FPU_IRQHandler                           0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    PCM_IRQHandler                           0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    PORT3_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    PSS_IRQHandler                           0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    RTC_C_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    T32_INTC_IRQHandler                      0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    TA0_0_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    TA0_N_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    TA1_N_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    TA2_N_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    TA3_0_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    WDT_A_IRQHandler                         0x00000173   Thumb Code     0  startup_msp432p401r_uvision.o(.text)
    __aeabi_memcpy                           0x0000017d   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x0000017d   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x0000017d   Thumb Code     0  memcpya.o(.text)
    __aeabi_dadd                             0x000001a1   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x000002e3   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x000002e9   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x000002ef   Thumb Code   228  dmul.o(.text)
    __aeabi_i2d                              0x000003d3   Thumb Code    34  dflti.o(.text)
    __aeabi_d2iz                             0x000003f5   Thumb Code    62  dfixi.o(.text)
    __aeabi_uidiv                            0x00000433   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x00000433   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x0000045f   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0000045f   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x0000047d   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x0000047d   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x0000049d   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x0000049d   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x000004c1   Thumb Code     0  iusefp.o(.text)
    _double_round                            0x000004c1   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x000004df   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x0000057d   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x0000057d   Thumb Code     0  init.o(.text)
    ADC14_IRQHandler                         0x000005a1   Thumb Code    66  adc.o(i.ADC14_IRQHandler)
    ADC14_getMultiSequenceResult             0x000005fd   Thumb Code    38  adc14.o(i.ADC14_getMultiSequenceResult)
    BEEP_Init                                0x00000629   Thumb Code    26  led.o(i.BEEP_Init)
    BEEP_Off                                 0x00000651   Thumb Code     8  led.o(i.BEEP_Off)
    BEEP_On                                  0x0000065d   Thumb Code     8  led.o(i.BEEP_On)
    CS_clearInterruptFlag                    0x00000669   Thumb Code    26  cs.o(i.CS_clearInterruptFlag)
    CS_getACLK                               0x0000068d   Thumb Code    20  cs.o(i.CS_getACLK)
    CS_getDCOFrequency                       0x000006a5   Thumb Code   234  cs.o(i.CS_getDCOFrequency)
    CS_getMCLK                               0x000007b1   Thumb Code    20  cs.o(i.CS_getMCLK)
    CS_getSMCLK                              0x000007c9   Thumb Code    18  cs.o(i.CS_getSMCLK)
    CS_setExternalClockSourceFrequency       0x000007e1   Thumb Code     8  cs.o(i.CS_setExternalClockSourceFrequency)
    CS_startHFXT                             0x000007ed   Thumb Code     6  cs.o(i.CS_startHFXT)
    CS_startHFXTWithTimeout                  0x000007f5   Thumb Code   144  cs.o(i.CS_startHFXTWithTimeout)
    CS_startLFXT                             0x00000895   Thumb Code     6  cs.o(i.CS_startLFXT)
    CS_startLFXTWithTimeout                  0x0000089d   Thumb Code   144  cs.o(i.CS_startLFXTWithTimeout)
    EUSCIA0_IRQHandler                       0x0000093d   Thumb Code    24  main.o(i.EUSCIA0_IRQHandler)
    FSM                                      0x0000095d   Thumb Code   530  fsm.o(i.FSM)
    GPIO_clearInterruptFlag                  0x00000bd1   Thumb Code    14  gpio.o(i.GPIO_clearInterruptFlag)
    GPIO_enableInterrupt                     0x00000be5   Thumb Code    14  gpio.o(i.GPIO_enableInterrupt)
    GPIO_getEnabledInterruptStatus           0x00000bf9   Thumb Code    52  gpio.o(i.GPIO_getEnabledInterruptStatus)
    GPIO_getInputPinValue                    0x00000c31   Thumb Code    16  gpio.o(i.GPIO_getInputPinValue)
    GPIO_getInterruptStatus                  0x00000c45   Thumb Code    12  gpio.o(i.GPIO_getInterruptStatus)
    GPIO_interruptEdgeSelect                 0x00000c55   Thumb Code    22  gpio.o(i.GPIO_interruptEdgeSelect)
    GPIO_setAsInputPin                       0x00000c71   Thumb Code    32  gpio.o(i.GPIO_setAsInputPin)
    GPIO_setAsOutputPin                      0x00000c95   Thumb Code    26  gpio.o(i.GPIO_setAsOutputPin)
    GPIO_setOutputHighOnPin                  0x00000cb5   Thumb Code    14  gpio.o(i.GPIO_setOutputHighOnPin)
    GPIO_setOutputLowOnPin                   0x00000cc9   Thumb Code    14  gpio.o(i.GPIO_setOutputLowOnPin)
    Grayscale_Init                           0x00000cdd   Thumb Code    70  grayscale.o(i.Grayscale_Init)
    Grayscale_Judge                          0x00000d25   Thumb Code   618  grayscale.o(i.Grayscale_Judge)
    Grayscale_scan                           0x00000fad   Thumb Code   118  grayscale.o(i.Grayscale_scan)
    I2C_Configuration                        0x0000102d   Thumb Code    76  oled.o(i.I2C_Configuration)
    I2C_masterSendMultiByteFinish            0x00001085   Thumb Code    52  i2c.o(i.I2C_masterSendMultiByteFinish)
    I2C_masterSendMultiByteStart             0x000010b9   Thumb Code    52  i2c.o(i.I2C_masterSendMultiByteStart)
    Incremental_PID_Calc                     0x000010ed   Thumb Code   114  pidcontrol.o(i.Incremental_PID_Calc)
    Incremental_PID_Init                     0x0000115f   Thumb Code    10  pidcontrol.o(i.Incremental_PID_Init)
    Inner_Ring                               0x00001169   Thumb Code   206  grayscale.o(i.Inner_Ring)
    Interrupt_enableInterrupt                0x00001255   Thumb Code    84  interrupt.o(i.Interrupt_enableInterrupt)
    Interrupt_setPriority                    0x000012b1   Thumb Code    30  interrupt.o(i.Interrupt_setPriority)
    KEY_Init                                 0x000012d5   Thumb Code    90  key.o(i.KEY_Init)
    KEY_Scan                                 0x00001335   Thumb Code    74  key.o(i.KEY_Scan)
    LED_B_Tog                                0x00001389   Thumb Code    14  led.o(i.LED_B_Tog)
    LED_G_Tog                                0x0000139d   Thumb Code    14  led.o(i.LED_G_Tog)
    LED_Init                                 0x000013b1   Thumb Code    46  led.o(i.LED_Init)
    LED_RED_Tog                              0x000013e9   Thumb Code    14  led.o(i.LED_RED_Tog)
    LED_R_Tog                                0x000013fd   Thumb Code    14  led.o(i.LED_R_Tog)
    Motor_Init                               0x00001411   Thumb Code    32  motor.o(i.Motor_Init)
    Motor_PID                                0x00001431   Thumb Code    94  motor.o(i.Motor_PID)
    Motor_Set                                0x000014ad   Thumb Code   130  motor.o(i.Motor_Set)
    OLED_Clear                               0x00001535   Thumb Code    60  oled.o(i.OLED_Clear)
    OLED_Init                                0x00001571   Thumb Code   238  oled.o(i.OLED_Init)
    OLED_Set_Pos                             0x0000165f   Thumb Code    40  oled.o(i.OLED_Set_Pos)
    OLED_ShowChar                            0x00001689   Thumb Code   128  oled.o(i.OLED_ShowChar)
    OLED_ShowNum                             0x00001711   Thumb Code   122  oled.o(i.OLED_ShowNum)
    OLED_WR_Byte                             0x0000178d   Thumb Code    28  oled.o(i.OLED_WR_Byte)
    Outer_Ring                               0x000017ad   Thumb Code   200  grayscale.o(i.Outer_Ring)
    PORT1_IRQHandler                         0x00001891   Thumb Code    46  key.o(i.PORT1_IRQHandler)
    PORT2_IRQHandler                         0x000018c9   Thumb Code   100  encoder.o(i.PORT2_IRQHandler)
    PORT4_IRQHandler                         0x00001931   Thumb Code   100  encoder.o(i.PORT4_IRQHandler)
    PORT5_IRQHandler                         0x00001999   Thumb Code   100  encoder.o(i.PORT5_IRQHandler)
    PORT6_IRQHandler                         0x00001a01   Thumb Code   100  encoder.o(i.PORT6_IRQHandler)
    SW_Encoder_Clear                         0x00001a69   Thumb Code    10  encoder.o(i.SW_Encoder_Clear)
    SW_Encoder_Init                          0x00001a79   Thumb Code   256  encoder.o(i.SW_Encoder_Init)
    SW_Encoder_Read                          0x00001b7d   Thumb Code    20  encoder.o(i.SW_Encoder_Read)
    SysCtl_A_disableNMISource                0x00001b9d   Thumb Code    10  sysctl_a.o(i.SysCtl_A_disableNMISource)
    SysCtl_A_enableNMISource                 0x00001bad   Thumb Code    10  sysctl_a.o(i.SysCtl_A_enableNMISource)
    SysCtl_A_getNMISourceStatus              0x00001bbd   Thumb Code    10  sysctl_a.o(i.SysCtl_A_getNMISourceStatus)
    SysCtl_A_getTLVInfo                      0x00001bcd   Thumb Code    64  sysctl_a.o(i.SysCtl_A_getTLVInfo)
    SysInit                                  0x00001c15   Thumb Code   126  sysinit.o(i.SysInit)
    SystemInit                               0x00001ca1   Thumb Code    76  system_msp432p401r.o(i.SystemInit)
    T32_INT1_IRQHandler                      0x00001d01   Thumb Code    10  tim32.o(i.T32_INT1_IRQHandler)
    T32_INT2_IRQHandler                      0x00001d15   Thumb Code    10  tim32.o(i.T32_INT2_IRQHandler)
    TA1_0_IRQHandler                         0x00001d29   Thumb Code   156  main.o(i.TA1_0_IRQHandler)
    TA2_0_IRQHandler                         0x00001df5   Thumb Code   198  main.o(i.TA2_0_IRQHandler)
    TA3_N_IRQHandler                         0x00001f01   Thumb Code   188  tima.o(i.TA3_N_IRQHandler)
    TimA0_PWM_Init                           0x00001fd5   Thumb Code    86  tima.o(i.TimA0_PWM_Init)
    TimA1_Int_Init                           0x00002035   Thumb Code    94  tima.o(i.TimA1_Int_Init)
    TimA2_Int_Init                           0x0000209d   Thumb Code    76  tima.o(i.TimA2_Int_Init)
    TimA3_Cap_Init                           0x000020f1   Thumb Code   118  tima.o(i.TimA3_Cap_Init)
    Timer_A_getCaptureCompareCount           0x00002175   Thumb Code    16  timer_a.o(i.Timer_A_getCaptureCompareCount)
    UART_clearInterruptFlag                  0x00002185   Thumb Code     8  uart.o(i.UART_clearInterruptFlag)
    UART_enableInterrupt                     0x0000218d   Thumb Code    22  uart.o(i.UART_enableInterrupt)
    UART_initModule                          0x000021a3   Thumb Code   146  uart.o(i.UART_initModule)
    UART_receiveData                         0x00002235   Thumb Code    24  uart.o(i.UART_receiveData)
    UART_transmitData                        0x0000224d   Thumb Code    22  uart.o(i.UART_transmitData)
    __0printf$1                              0x000023bd   Thumb Code    22  printf1.o(i.__0printf$1)
    __1printf$1                              0x000023bd   Thumb Code     0  printf1.o(i.__0printf$1)
    __2printf                                0x000023bd   Thumb Code     0  printf1.o(i.__0printf$1)
    __scatterload_copy                       0x000023dd   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x000023eb   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x000023ed   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    bitPosition                              0x00002551   Thumb Code    16  baudrate_calculate.o(i.bitPosition)
    delay_init                               0x00002561   Thumb Code    30  delay.o(i.delay_init)
    delay_ms                                 0x00002589   Thumb Code    24  delay.o(i.delay_ms)
    delay_us                                 0x000025a1   Thumb Code    46  delay.o(i.delay_us)
    encoder_to_speed                         0x000025d5   Thumb Code    54  encoder.o(i.encoder_to_speed)
    eusci_calcBaudDividers                   0x0000261d   Thumb Code   282  baudrate_calculate.o(i.eusci_calcBaudDividers)
    fputc                                    0x00002749   Thumb Code    16  usart.o(i.fputc)
    main                                     0x00002789   Thumb Code   228  main.o(i.main)
    myabs                                    0x0000288d   Thumb Code     8  globalvar.o(i.myabs)
    oled_pow                                 0x00002895   Thumb Code    16  oled.o(i.oled_pow)
    system_init                              0x000028a5   Thumb Code   308  main.o(i.system_init)
    uart_init                                0x00002a1d   Thumb Code    78  usart.o(i.uart_init)
    ult_start                                0x00002a79   Thumb Code    28  tima.o(i.ult_start)
    asc2_0806                                0x00002a94   Data         570  oled.o(.constdata)
    asc2_1608                                0x00002cce   Data        1520  oled.o(.constdata)
    Hzk                                      0x000032be   Data         224  oled.o(.constdata)
    Region$$Table$$Base                      0x00003520   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00003540   Number         0  anon$$obj.o(Region$$Table)
    key_flag                                 0x20000000   Data           4  main.o(.data)
    TIMA3_CAP_STA                            0x2000000c   Data           1  tima.o(.data)
    TIMA3_CAP_VAL                            0x2000000e   Data           2  tima.o(.data)
    time                                     0x20000010   Data           4  tima.o(.data)
    time_cnt                                 0x20000014   Data           4  tima.o(.data)
    __stdout                                 0x20000018   Data           4  usart.o(.data)
    encoder_l                                0x20000020   Data           4  encoder.o(.data)
    encoder_r                                0x20000024   Data           4  encoder.o(.data)
    encoder_left                             0x20000028   Data           4  globalvar.o(.data)
    encoder_right                            0x2000002c   Data           4  globalvar.o(.data)
    speed_left                               0x20000030   Data           4  globalvar.o(.data)
    speed_right                              0x20000034   Data           4  globalvar.o(.data)
    ult_distance                             0x20000038   Data           4  globalvar.o(.data)
    road                                     0x2000003c   Data           4  globalvar.o(.data)
    road_flag                                0x20000040   Data           4  globalvar.o(.data)
    lap                                      0x20000044   Data           4  globalvar.o(.data)
    task                                     0x20000048   Data           4  globalvar.o(.data)
    cross_flag                               0x2000004c   Data           4  globalvar.o(.data)
    wait_flag                                0x20000050   Data           4  globalvar.o(.data)
    wait_time                                0x20000054   Data           4  globalvar.o(.data)
    send_flag                                0x20000058   Data           4  globalvar.o(.data)
    last_send_flag                           0x2000005c   Data           4  globalvar.o(.data)
    movement                                 0x20000060   Data           4  globalvar.o(.data)
    movement_left                            0x20000064   Data           4  globalvar.o(.data)
    movement_right                           0x20000068   Data           4  globalvar.o(.data)
    motor_flag                               0x2000006c   Data           4  globalvar.o(.data)
    gray_state                               0x20000070   Data           4  globalvar.o(.data)
    small_kdiff                              0x20000074   Data           4  globalvar.o(.data)
    medium_kdiff                             0x20000078   Data           4  globalvar.o(.data)
    large_kdiff                              0x2000007c   Data           4  globalvar.o(.data)
    usart0_rx_data                           0x20000080   Data           4  globalvar.o(.data)
    beep_flag                                0x20000084   Data           4  globalvar.o(.data)
    CHA                                      0x20000088   Data           4  grayscale.o(.data)
    CHB                                      0x2000008c   Data           4  grayscale.o(.data)
    CHC                                      0x20000090   Data           4  grayscale.o(.data)
    CHD                                      0x20000094   Data           4  grayscale.o(.data)
    CHE                                      0x20000098   Data           4  grayscale.o(.data)
    CHF                                      0x2000009c   Data           4  grayscale.o(.data)
    CHG                                      0x200000a0   Data           4  grayscale.o(.data)
    CHH                                      0x200000a4   Data           4  grayscale.o(.data)
    state                                    0x200000a8   Data           1  fsm.o(.data)
    stateCROSS                               0x200000a9   Data           1  fsm.o(.data)
    pid_left                                 0x200000b4   Data          40  globalvar.o(.bss)
    pid_right                                0x200000dc   Data          40  globalvar.o(.bss)
    __initial_sp                             0x20000308   Data           0  startup_msp432p401r_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x00000145

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x000035f4, Max: 0x00040000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00003540, Max: 0x00040000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x00000144   Data   RO           73    RESET               startup_msp432p401r_uvision.o
    0x00000144   0x00000144   0x00000000   Code   RO         2704  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x00000144   0x00000144   0x00000004   Code   RO         2980    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x00000148   0x00000148   0x00000004   Code   RO         2983    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO         2985    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x0000014c   0x0000014c   0x00000000   Code   RO         2987    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x0000014c   0x0000014c   0x00000008   Code   RO         2988    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x00000154   0x00000154   0x00000000   Code   RO         2990    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x00000154   0x00000154   0x00000000   Code   RO         2992    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x00000154   0x00000154   0x00000004   Code   RO         2981    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x00000158   0x00000158   0x00000024   Code   RO           74    .text               startup_msp432p401r_uvision.o
    0x0000017c   0x0000017c   0x00000024   Code   RO         2707    .text               mc_w.l(memcpya.o)
    0x000001a0   0x000001a0   0x0000014e   Code   RO         2972    .text               mf_w.l(dadd.o)
    0x000002ee   0x000002ee   0x000000e4   Code   RO         2974    .text               mf_w.l(dmul.o)
    0x000003d2   0x000003d2   0x00000022   Code   RO         2976    .text               mf_w.l(dflti.o)
    0x000003f4   0x000003f4   0x0000003e   Code   RO         2978    .text               mf_w.l(dfixi.o)
    0x00000432   0x00000432   0x0000002c   Code   RO         2994    .text               mc_w.l(uidiv.o)
    0x0000045e   0x0000045e   0x0000001e   Code   RO         2998    .text               mc_w.l(llshl.o)
    0x0000047c   0x0000047c   0x00000020   Code   RO         3000    .text               mc_w.l(llushr.o)
    0x0000049c   0x0000049c   0x00000024   Code   RO         3002    .text               mc_w.l(llsshr.o)
    0x000004c0   0x000004c0   0x00000000   Code   RO         3004    .text               mc_w.l(iusefp.o)
    0x000004c0   0x000004c0   0x000000ba   Code   RO         3005    .text               mf_w.l(depilogue.o)
    0x0000057a   0x0000057a   0x00000002   PAD
    0x0000057c   0x0000057c   0x00000024   Code   RO         3013    .text               mc_w.l(init.o)
    0x000005a0   0x000005a0   0x0000005c   Code   RO          278    i.ADC14_IRQHandler  adc.o
    0x000005fc   0x000005fc   0x0000002c   Code   RO         1177    i.ADC14_getMultiSequenceResult  msp432p4xx_driverlib.lib(adc14.o)
    0x00000628   0x00000628   0x00000028   Code   RO          389    i.BEEP_Init         led.o
    0x00000650   0x00000650   0x0000000c   Code   RO          390    i.BEEP_Off          led.o
    0x0000065c   0x0000065c   0x0000000c   Code   RO          391    i.BEEP_On           led.o
    0x00000668   0x00000668   0x00000024   Code   RO         1431    i.CS_clearInterruptFlag  msp432p4xx_driverlib.lib(cs.o)
    0x0000068c   0x0000068c   0x00000018   Code   RO         1440    i.CS_getACLK        msp432p4xx_driverlib.lib(cs.o)
    0x000006a4   0x000006a4   0x0000010c   Code   RO         1442    i.CS_getDCOFrequency  msp432p4xx_driverlib.lib(cs.o)
    0x000007b0   0x000007b0   0x00000018   Code   RO         1446    i.CS_getMCLK        msp432p4xx_driverlib.lib(cs.o)
    0x000007c8   0x000007c8   0x00000018   Code   RO         1447    i.CS_getSMCLK       msp432p4xx_driverlib.lib(cs.o)
    0x000007e0   0x000007e0   0x0000000c   Code   RO         1454    i.CS_setExternalClockSourceFrequency  msp432p4xx_driverlib.lib(cs.o)
    0x000007ec   0x000007ec   0x00000006   Code   RO         1457    i.CS_startHFXT      msp432p4xx_driverlib.lib(cs.o)
    0x000007f2   0x000007f2   0x00000002   PAD
    0x000007f4   0x000007f4   0x000000a0   Code   RO         1458    i.CS_startHFXTWithTimeout  msp432p4xx_driverlib.lib(cs.o)
    0x00000894   0x00000894   0x00000006   Code   RO         1459    i.CS_startLFXT      msp432p4xx_driverlib.lib(cs.o)
    0x0000089a   0x0000089a   0x00000002   PAD
    0x0000089c   0x0000089c   0x000000a0   Code   RO         1460    i.CS_startLFXTWithTimeout  msp432p4xx_driverlib.lib(cs.o)
    0x0000093c   0x0000093c   0x00000020   Code   RO           81    i.EUSCIA0_IRQHandler  main.o
    0x0000095c   0x0000095c   0x00000274   Code   RO         1132    i.FSM               fsm.o
    0x00000bd0   0x00000bd0   0x00000014   Code   RO         1657    i.GPIO_clearInterruptFlag  msp432p4xx_driverlib.lib(gpio.o)
    0x00000be4   0x00000be4   0x00000014   Code   RO         1659    i.GPIO_enableInterrupt  msp432p4xx_driverlib.lib(gpio.o)
    0x00000bf8   0x00000bf8   0x00000038   Code   RO         1660    i.GPIO_getEnabledInterruptStatus  msp432p4xx_driverlib.lib(gpio.o)
    0x00000c30   0x00000c30   0x00000014   Code   RO         1661    i.GPIO_getInputPinValue  msp432p4xx_driverlib.lib(gpio.o)
    0x00000c44   0x00000c44   0x00000010   Code   RO         1662    i.GPIO_getInterruptStatus  msp432p4xx_driverlib.lib(gpio.o)
    0x00000c54   0x00000c54   0x0000001c   Code   RO         1663    i.GPIO_interruptEdgeSelect  msp432p4xx_driverlib.lib(gpio.o)
    0x00000c70   0x00000c70   0x00000024   Code   RO         1665    i.GPIO_setAsInputPin  msp432p4xx_driverlib.lib(gpio.o)
    0x00000c94   0x00000c94   0x00000020   Code   RO         1668    i.GPIO_setAsOutputPin  msp432p4xx_driverlib.lib(gpio.o)
    0x00000cb4   0x00000cb4   0x00000014   Code   RO         1673    i.GPIO_setOutputHighOnPin  msp432p4xx_driverlib.lib(gpio.o)
    0x00000cc8   0x00000cc8   0x00000014   Code   RO         1674    i.GPIO_setOutputLowOnPin  msp432p4xx_driverlib.lib(gpio.o)
    0x00000cdc   0x00000cdc   0x00000046   Code   RO         1078    i.Grayscale_Init    grayscale.o
    0x00000d22   0x00000d22   0x00000002   PAD
    0x00000d24   0x00000d24   0x00000288   Code   RO         1079    i.Grayscale_Judge   grayscale.o
    0x00000fac   0x00000fac   0x00000080   Code   RO         1080    i.Grayscale_scan    grayscale.o
    0x0000102c   0x0000102c   0x00000058   Code   RO          529    i.I2C_Configuration  oled.o
    0x00001084   0x00001084   0x00000034   Code   RO         1824    i.I2C_masterSendMultiByteFinish  msp432p4xx_driverlib.lib(i2c.o)
    0x000010b8   0x000010b8   0x00000034   Code   RO         1828    i.I2C_masterSendMultiByteStart  msp432p4xx_driverlib.lib(i2c.o)
    0x000010ec   0x000010ec   0x00000072   Code   RO         1045    i.Incremental_PID_Calc  pidcontrol.o
    0x0000115e   0x0000115e   0x0000000a   Code   RO         1046    i.Incremental_PID_Init  pidcontrol.o
    0x00001168   0x00001168   0x000000ec   Code   RO         1081    i.Inner_Ring        grayscale.o
    0x00001254   0x00001254   0x0000005c   Code   RO         2086    i.Interrupt_enableInterrupt  msp432p4xx_driverlib.lib(interrupt.o)
    0x000012b0   0x000012b0   0x00000024   Code   RO         2096    i.Interrupt_setPriority  msp432p4xx_driverlib.lib(interrupt.o)
    0x000012d4   0x000012d4   0x00000060   Code   RO          313    i.KEY_Init          key.o
    0x00001334   0x00001334   0x00000054   Code   RO          314    i.KEY_Scan          key.o
    0x00001388   0x00001388   0x00000014   Code   RO          394    i.LED_B_Tog         led.o
    0x0000139c   0x0000139c   0x00000014   Code   RO          398    i.LED_G_Tog         led.o
    0x000013b0   0x000013b0   0x00000038   Code   RO          399    i.LED_Init          led.o
    0x000013e8   0x000013e8   0x00000014   Code   RO          403    i.LED_RED_Tog       led.o
    0x000013fc   0x000013fc   0x00000014   Code   RO          406    i.LED_R_Tog         led.o
    0x00001410   0x00001410   0x00000020   Code   RO          955    i.Motor_Init        motor.o
    0x00001430   0x00001430   0x0000007c   Code   RO          956    i.Motor_PID         motor.o
    0x000014ac   0x000014ac   0x00000088   Code   RO          957    i.Motor_Set         motor.o
    0x00001534   0x00001534   0x0000003c   Code   RO          530    i.OLED_Clear        oled.o
    0x00001570   0x00001570   0x000000ee   Code   RO          536    i.OLED_Init         oled.o
    0x0000165e   0x0000165e   0x00000028   Code   RO          537    i.OLED_Set_Pos      oled.o
    0x00001686   0x00001686   0x00000002   PAD
    0x00001688   0x00001688   0x00000088   Code   RO          538    i.OLED_ShowChar     oled.o
    0x00001710   0x00001710   0x0000007a   Code   RO          540    i.OLED_ShowNum      oled.o
    0x0000178a   0x0000178a   0x00000002   PAD
    0x0000178c   0x0000178c   0x00000020   Code   RO          542    i.OLED_WR_Byte      oled.o
    0x000017ac   0x000017ac   0x000000e4   Code   RO         1082    i.Outer_Ring        grayscale.o
    0x00001890   0x00001890   0x00000038   Code   RO          315    i.PORT1_IRQHandler  key.o
    0x000018c8   0x000018c8   0x00000068   Code   RO          886    i.PORT2_IRQHandler  encoder.o
    0x00001930   0x00001930   0x00000068   Code   RO          887    i.PORT4_IRQHandler  encoder.o
    0x00001998   0x00001998   0x00000068   Code   RO          888    i.PORT5_IRQHandler  encoder.o
    0x00001a00   0x00001a00   0x00000068   Code   RO          889    i.PORT6_IRQHandler  encoder.o
    0x00001a68   0x00001a68   0x00000010   Code   RO          890    i.SW_Encoder_Clear  encoder.o
    0x00001a78   0x00001a78   0x00000104   Code   RO          891    i.SW_Encoder_Init   encoder.o
    0x00001b7c   0x00001b7c   0x00000020   Code   RO          892    i.SW_Encoder_Read   encoder.o
    0x00001b9c   0x00001b9c   0x00000010   Code   RO         2576    i.SysCtl_A_disableNMISource  msp432p4xx_driverlib.lib(sysctl_a.o)
    0x00001bac   0x00001bac   0x00000010   Code   RO         2581    i.SysCtl_A_enableNMISource  msp432p4xx_driverlib.lib(sysctl_a.o)
    0x00001bbc   0x00001bbc   0x00000010   Code   RO         2587    i.SysCtl_A_getNMISourceStatus  msp432p4xx_driverlib.lib(sysctl_a.o)
    0x00001bcc   0x00001bcc   0x00000048   Code   RO         2589    i.SysCtl_A_getTLVInfo  msp432p4xx_driverlib.lib(sysctl_a.o)
    0x00001c14   0x00001c14   0x0000008c   Code   RO          794    i.SysInit           sysinit.o
    0x00001ca0   0x00001ca0   0x00000060   Code   RO            5    i.SystemInit        system_msp432p401r.o
    0x00001d00   0x00001d00   0x00000014   Code   RO          641    i.T32_INT1_IRQHandler  tim32.o
    0x00001d14   0x00001d14   0x00000014   Code   RO          642    i.T32_INT2_IRQHandler  tim32.o
    0x00001d28   0x00001d28   0x000000cc   Code   RO           82    i.TA1_0_IRQHandler  main.o
    0x00001df4   0x00001df4   0x0000010c   Code   RO           83    i.TA2_0_IRQHandler  main.o
    0x00001f00   0x00001f00   0x000000d4   Code   RO          683    i.TA3_N_IRQHandler  tima.o
    0x00001fd4   0x00001fd4   0x00000060   Code   RO          684    i.TimA0_PWM_Init    tima.o
    0x00002034   0x00002034   0x00000068   Code   RO          685    i.TimA1_Int_Init    tima.o
    0x0000209c   0x0000209c   0x00000054   Code   RO          686    i.TimA2_Int_Init    tima.o
    0x000020f0   0x000020f0   0x00000084   Code   RO          687    i.TimA3_Cap_Init    tima.o
    0x00002174   0x00002174   0x00000010   Code   RO         2236    i.Timer_A_getCaptureCompareCount  msp432p4xx_driverlib.lib(timer_a.o)
    0x00002184   0x00002184   0x00000008   Code   RO         2412    i.UART_clearInterruptFlag  msp432p4xx_driverlib.lib(uart.o)
    0x0000218c   0x0000218c   0x00000016   Code   RO         2415    i.UART_enableInterrupt  msp432p4xx_driverlib.lib(uart.o)
    0x000021a2   0x000021a2   0x00000092   Code   RO         2421    i.UART_initModule   msp432p4xx_driverlib.lib(uart.o)
    0x00002234   0x00002234   0x00000018   Code   RO         2423    i.UART_receiveData  msp432p4xx_driverlib.lib(uart.o)
    0x0000224c   0x0000224c   0x00000016   Code   RO         2430    i.UART_transmitData  msp432p4xx_driverlib.lib(uart.o)
    0x00002262   0x00002262   0x00000002   PAD
    0x00002264   0x00002264   0x000000c0   Code   RO         1463    i._CSComputeCLKFrequency  msp432p4xx_driverlib.lib(cs.o)
    0x00002324   0x00002324   0x00000098   Code   RO         1464    i._CSGetHFXTFrequency  msp432p4xx_driverlib.lib(cs.o)
    0x000023bc   0x000023bc   0x00000020   Code   RO         2756    i.__0printf$1       mc_w.l(printf1.o)
    0x000023dc   0x000023dc   0x0000000e   Code   RO         3017    i.__scatterload_copy  mc_w.l(handlers.o)
    0x000023ea   0x000023ea   0x00000002   Code   RO         3018    i.__scatterload_null  mc_w.l(handlers.o)
    0x000023ec   0x000023ec   0x0000000e   Code   RO         3019    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x000023fa   0x000023fa   0x00000002   PAD
    0x000023fc   0x000023fc   0x00000154   Code   RO         2763    i._printf_core      mc_w.l(printf1.o)
    0x00002550   0x00002550   0x00000010   Code   RO          856    i.bitPosition       baudrate_calculate.o
    0x00002560   0x00002560   0x00000028   Code   RO          818    i.delay_init        delay.o
    0x00002588   0x00002588   0x00000018   Code   RO          819    i.delay_ms          delay.o
    0x000025a0   0x000025a0   0x00000034   Code   RO          820    i.delay_us          delay.o
    0x000025d4   0x000025d4   0x00000048   Code   RO          893    i.encoder_to_speed  encoder.o
    0x0000261c   0x0000261c   0x0000012c   Code   RO          857    i.eusci_calcBaudDividers  baudrate_calculate.o
    0x00002748   0x00002748   0x00000014   Code   RO          742    i.fputc             usart.o
    0x0000275c   0x0000275c   0x0000002a   Code   RO          316    i.key_delay         key.o
    0x00002786   0x00002786   0x00000002   PAD
    0x00002788   0x00002788   0x00000104   Code   RO           84    i.main              main.o
    0x0000288c   0x0000288c   0x00000008   Code   RO          991    i.myabs             globalvar.o
    0x00002894   0x00002894   0x00000010   Code   RO          543    i.oled_pow          oled.o
    0x000028a4   0x000028a4   0x00000178   Code   RO           85    i.system_init       main.o
    0x00002a1c   0x00002a1c   0x0000005c   Code   RO          744    i.uart_init         usart.o
    0x00002a78   0x00002a78   0x0000001c   Code   RO          688    i.ult_start         tima.o
    0x00002a94   0x00002a94   0x00000920   Data   RO          544    .constdata          oled.o
    0x000033b4   0x000033b4   0x00000040   Data   RO          689    .constdata          tima.o
    0x000033f4   0x000033f4   0x00000050   Data   RO          745    .constdata          usart.o
    0x00003444   0x00003444   0x0000004c   Data   RO         1677    .constdata          msp432p4xx_driverlib.lib(gpio.o)
    0x00003490   0x00003490   0x00000090   Data   RO         2102    .constdata          msp432p4xx_driverlib.lib(interrupt.o)
    0x00003520   0x00003520   0x00000020   Data   RO         3015    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x00003540, Size: 0x00000308, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x00003540   0x00000004   Data   RW           86    .data               main.o
    0x20000004   0x00003544   0x00000004   Data   RW          280    .data               adc.o
    0x20000008   0x00003548   0x00000001   Data   RW          317    .data               key.o
    0x20000009   0x00003549   0x00000003   PAD
    0x2000000c   0x0000354c   0x0000000c   Data   RW          690    .data               tima.o
    0x20000018   0x00003558   0x00000004   Data   RW          746    .data               usart.o
    0x2000001c   0x0000355c   0x00000001   Data   RW          821    .data               delay.o
    0x2000001d   0x0000355d   0x00000003   PAD
    0x20000020   0x00003560   0x00000008   Data   RW          894    .data               encoder.o
    0x20000028   0x00003568   0x00000004   Data   RW          994    .data               globalvar.o
    0x2000002c   0x0000356c   0x00000004   Data   RW          995    .data               globalvar.o
    0x20000030   0x00003570   0x00000004   Data   RW          996    .data               globalvar.o
    0x20000034   0x00003574   0x00000004   Data   RW          997    .data               globalvar.o
    0x20000038   0x00003578   0x00000004   Data   RW          998    .data               globalvar.o
    0x2000003c   0x0000357c   0x00000004   Data   RW          999    .data               globalvar.o
    0x20000040   0x00003580   0x00000004   Data   RW         1000    .data               globalvar.o
    0x20000044   0x00003584   0x00000004   Data   RW         1001    .data               globalvar.o
    0x20000048   0x00003588   0x00000004   Data   RW         1002    .data               globalvar.o
    0x2000004c   0x0000358c   0x00000004   Data   RW         1003    .data               globalvar.o
    0x20000050   0x00003590   0x00000004   Data   RW         1004    .data               globalvar.o
    0x20000054   0x00003594   0x00000004   Data   RW         1005    .data               globalvar.o
    0x20000058   0x00003598   0x00000004   Data   RW         1006    .data               globalvar.o
    0x2000005c   0x0000359c   0x00000004   Data   RW         1007    .data               globalvar.o
    0x20000060   0x000035a0   0x00000004   Data   RW         1008    .data               globalvar.o
    0x20000064   0x000035a4   0x00000004   Data   RW         1009    .data               globalvar.o
    0x20000068   0x000035a8   0x00000004   Data   RW         1010    .data               globalvar.o
    0x2000006c   0x000035ac   0x00000004   Data   RW         1013    .data               globalvar.o
    0x20000070   0x000035b0   0x00000004   Data   RW         1014    .data               globalvar.o
    0x20000074   0x000035b4   0x00000004   Data   RW         1015    .data               globalvar.o
    0x20000078   0x000035b8   0x00000004   Data   RW         1016    .data               globalvar.o
    0x2000007c   0x000035bc   0x00000004   Data   RW         1017    .data               globalvar.o
    0x20000080   0x000035c0   0x00000004   Data   RW         1018    .data               globalvar.o
    0x20000084   0x000035c4   0x00000004   Data   RW         1019    .data               globalvar.o
    0x20000088   0x000035c8   0x00000020   Data   RW         1083    .data               grayscale.o
    0x200000a8   0x000035e8   0x00000002   Data   RW         1133    .data               fsm.o
    0x200000aa   0x000035ea   0x00000002   PAD
    0x200000ac   0x000035ec   0x00000008   Data   RW         1465    .data               msp432p4xx_driverlib.lib(cs.o)
    0x200000b4        -       0x00000028   Zero   RW          992    .bss                globalvar.o
    0x200000dc        -       0x00000028   Zero   RW          993    .bss                globalvar.o
    0x20000104   0x000035f4   0x00000004   PAD
    0x20000108        -       0x00000200   Zero   RW           71    STACK               startup_msp432p401r_uvision.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        92         26          0          4          0       1123   adc.o
       316         18          0          0          0       2187   baudrate_calculate.o
       116         16          0          1          0       1707   delay.o
       796         56          0          8          0       5112   encoder.o
       628         98          0          2          0       1273   fsm.o
         8          0          0         96         80       1737   globalvar.o
      1310         98          0         32          0       3640   grayscale.o
       278         26          0          1          0       2645   key.o
       200         56          0          0          0       4221   led.o
      1140        226          0          4          0      18291   main.o
       292         36          0          0          0       1777   motor.o
       732         24       2336          0          0       6227   oled.o
       124          0          0          0          0       1590   pidcontrol.o
        36          8        324          0        512        820   startup_msp432p401r_uvision.o
       140         14          0          0          0        518   sysinit.o
        96         20          0          0          0       5045   system_msp432p401r.o
        40         20          0          0          0        932   tim32.o
       656         66         64         12          0       4479   tima.o
       112         18         80          4          0       2874   usart.o

    ----------------------------------------------------------------------
      7120        <USER>       <GROUP>        172        596      66198   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         8          0          0          8          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0         68   memcpya.o
       372         14          0          0          0        184   printf1.o
        44          0          0          0          0         80   uidiv.o
       334          0          0          0          0        148   dadd.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        44          6          0          0          0       4582   adc14.o
      1064        170          0          8          0      13545   cs.o
       268         64         76          0          0       8073   gpio.o
       104          0          0          0          0       1527   i2c.o
       128         14        144          0          0       2468   interrupt.o
       120         26          0          0          0       3378   sysctl_a.o
        16          0          0          0          0        725   timer_a.o
       222          0          0          0          0       7860   uart.o

    ----------------------------------------------------------------------
      3456        <USER>        <GROUP>          8          0      43338   Library Totals
        10          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       636         30          0          0          0        604   mc_w.l
       844          0          0          0          0        576   mf_w.l
      1966        280        220          8          0      42158   msp432p4xx_driverlib.lib

    ----------------------------------------------------------------------
      3456        <USER>        <GROUP>          8          0      43338   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10576       1136       3056        180        596     100168   Grand Totals
     10576       1136       3056        180        596     100168   ELF Image Totals
     10576       1136       3056        180          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13632 (  13.31kB)
    Total RW  Size (RW Data + ZI Data)               776 (   0.76kB)
    Total ROM Size (Code + RO Data + RW Data)      13812 (  13.49kB)

==============================================================================

