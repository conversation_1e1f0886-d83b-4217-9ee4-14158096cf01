# STM32简化版双轮小车工程功能及引脚配置说明

## 工程概述
本工程是基于STM32F103C8T6微控制器的简化版双轮小车项目，仅保留左前轮和右前轮控制功能。通过按键分别控制两个轮子的转动，实现基础的运动控制。

## 主要功能模块

### 1. 电机驱动模块 (Motor.c/Motor.h)
**功能描述：**
- 双轮独立PWM控制
- 支持正转、反转、停止
- 速度范围：-100~100

**引脚配置：**
```
PWM输出引脚 (TIM2):
- PA0 (TIM2_CH1) - 左前轮PWM
- PA2 (TIM2_CH3) - 右前轮PWM

方向控制引脚:
- PB0/PB1   - 左前轮方向控制
- PB12/PB13 - 右前轮方向控制
```

**PWM参数：**
- 频率：20kHz (ARR=100, PSC=36)
- 占空比：0-100可调

### 2. 按键输入模块 (Key.c/Key.h)
**功能描述：**
- 两个独立按键控制电机
- Key1控制左前轮
- Key2控制右前轮

**引脚配置：**
```
按键输入引脚 (上拉输入):
- PA4 - Key1 (控制左前轮)
- PA5 - Key2 (控制右前轮)
```

**控制逻辑：**
- 按下Key1：左前轮以80%速度正转
- 松开Key1：左前轮停止
- 按下Key2：右前轮以80%速度正转
- 松开Key2：右前轮停止

## 系统配置

### 时钟配置
- 系统时钟：72MHz
- APB1时钟：36MHz (TIM2)
- APB2时钟：72MHz (GPIO)

### 中断优先级
- 中断分组：NVIC_PriorityGroup_2

### 定时器配置
- **TIM2 (电机PWM)：**
  - 预分频：36-1
  - 自动重装：100-1
  - PWM频率：20kHz
  - 使用通道：CH1(左前轮), CH3(右前轮)

## 主程序流程

```c
int main(void)
{
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);  // 中断优先级配置
    Motor_Init();                                    // 电机初始化
    Key_Init();                                      // 按键初始化

    while (1)
    {
        Key_GetNum();                                // 获取按键状态

        // Key1控制左前轮
        if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_4) == 0)
            LeftWheelFront_Speed(80);                // 左前轮正转
        else
            LeftWheelFront_Speed(0);                 // 左前轮停止

        // Key2控制右前轮
        if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_5) == 0)
            RightWheelFront_Speed(80);               // 右前轮正转
        else
            RightWheelFront_Speed(0);                // 右前轮停止

        Delay_ms(10);                                // 延时防抖
    }
}
```

## 编译环境
- 开发环境：Keil MDK-ARM
- 芯片型号：STM32F103C8T6
- 标准库版本：STM32F10x_StdPeriph_Lib_V3.5.0

## 注意事项
1. 电机驱动电压建议5V，逻辑电压3.3V
2. 按键采用上拉输入，按下时为低电平
3. PWM占空比范围0-100，对应电机速度0-100%
4. 已屏蔽所有其他功能模块，仅保留基础电机和按键控制

## 功能特点
- **简化设计**：仅保留核心的双轮控制功能
- **按键控制**：直观的按键操作方式
- **实时响应**：10ms循环检测，响应迅速
- **安全可靠**：松开按键立即停止，防止失控

## 扩展建议
- 可添加速度调节功能（通过按键切换不同速度档位）
- 可增加方向控制（正转/反转切换）
- 可集成编码器实现精确位置控制
- 可添加无线控制模块（蓝牙/WiFi）
