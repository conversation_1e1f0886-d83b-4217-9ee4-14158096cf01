#ifndef __GRAYSCALESENSOR_H
#define __GRAYSCALESENSOR_H

#include "stm32f10x.h"                  // Device header
#include "Motor.h"

void motor(int Z_Lun,int Y_Lun);
void GrayscaleSensor_Init(void);
void Timer_Init(void);

#define D1        		GPIO_ReadInputDataBit(GPIOC,GPIO_Pin_14)
#define D2        		GPIO_ReadInputDataBit(GPIOC,GPIO_Pin_13)
#define D3        		GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_7)
#define D4         		GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_6)
#define D5        		GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_5)
#define D6        		GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_4)
#define D7        		GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_3)





#endif
