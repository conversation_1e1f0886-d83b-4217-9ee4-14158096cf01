# STM32双轮小车使用说明文档

## 📖 项目简介

本项目是基于STM32F103C8T6微控制器的简化版双轮小车控制系统。通过两个按键分别控制左前轮和右前轮的转动，实现基础的运动控制功能。

### 🎯 设计目标
- **简单易用**：通过按键直接控制电机
- **响应迅速**：实时检测按键状态
- **安全可靠**：松开按键立即停止
- **易于扩展**：预留接口便于功能扩展

## 🔌 硬件连接

### 电机连接
```
左前轮电机：
- 电机PWM信号 → PA0 (TIM2_CH1)
- 电机方向控制 → PB0, PB1
- 电机电源 → 5V, GND

右前轮电机：
- 电机PWM信号 → PA2 (TIM2_CH3)  
- 电机方向控制 → PB12, PB13
- 电机电源 → 5V, GND
```

### 按键连接
```
控制按键：
- Key1 (左前轮控制) → PA4 (上拉输入)
- Key2 (右前轮控制) → PA5 (上拉输入)
- 按键另一端 → GND
```

### 电源连接
```
STM32供电：
- VCC → 3.3V
- GND → GND

电机驱动供电：
- VCC → 5V (推荐)
- GND → GND (与STM32共地)
```

## 🚀 快速开始

### 1. 环境准备
- **开发环境**：Keil MDK-ARM 5.0+
- **调试器**：ST-Link V2
- **芯片**：STM32F103C8T6
- **标准库**：STM32F10x_StdPeriph_Lib_V3.5.0

### 2. 编译下载
1. 打开Keil工程文件 `Project.uvprojx`
2. 选择目标芯片：STM32F103C8
3. 编译工程：Build → Rebuild All Target Files
4. 连接ST-Link调试器
5. 下载程序：Flash → Download

### 3. 硬件测试
1. 按照接线图连接硬件
2. 上电启动系统
3. 按下PA4按键，观察左前轮是否转动
4. 按下PA5按键，观察右前轮是否转动
5. 松开按键，确认电机立即停止

## 🎮 操作说明

### 基本操作
| 操作 | 按键 | 效果 |
|------|------|------|
| 控制左前轮 | 按住PA4 | 左前轮以80%速度正转 |
| 停止左前轮 | 松开PA4 | 左前轮立即停止 |
| 控制右前轮 | 按住PA5 | 右前轮以80%速度正转 |
| 停止右前轮 | 松开PA5 | 右前轮立即停止 |

### 运动模式
| 按键组合 | 运动效果 |
|----------|----------|
| 无按键 | 静止状态 |
| 仅PA4 | 左转 (左前轮转动) |
| 仅PA5 | 右转 (右前轮转动) |
| PA4+PA5 | 前进 (双轮同时转动) |

### 安全提示
⚠️ **重要安全事项**
- 确保电机电源电压不超过额定值
- 测试时请将小车架空，避免意外移动
- 长时间使用时注意电机温度
- 断电后再进行硬件连接

## ⚙️ 技术参数

### 系统配置
```
微控制器：STM32F103C8T6
系统时钟：72MHz
PWM频率：20kHz
控制周期：10ms
电机速度：0-100% (可调)
```

### 引脚分配
```
PWM输出：
- PA0 (TIM2_CH1) - 左前轮PWM
- PA2 (TIM2_CH3) - 右前轮PWM

方向控制：
- PB0/PB1 - 左前轮方向
- PB12/PB13 - 右前轮方向

按键输入：
- PA4 - 左前轮控制按键
- PA5 - 右前轮控制按键
```

## 🔧 代码结构

### 主要文件
```
User/
├── main.c              # 主程序文件
├── stm32f10x_conf.h    # 配置文件
└── stm32f10x_it.c      # 中断处理

Hardware/
├── Motor.c/Motor.h     # 电机控制模块
├── Key.c/Key.h         # 按键检测模块
└── Delay.c/Delay.h     # 延时函数

System/
└── Delay.c/Delay.h     # 系统延时

Start/
├── stm32f10x.h         # 芯片头文件
├── system_stm32f10x.c  # 系统初始化
└── startup_stm32f10x_md.s # 启动文件
```

### 核心函数
```c
// 电机控制
void Motor_Init(void);                    // 电机初始化
void LeftWheelFront_Speed(int Speed);     // 左前轮速度控制
void RightWheelFront_Speed(int Speed);    // 右前轮速度控制

// 按键检测  
void Key_Init(void);                      // 按键初始化
void Key_GetNum(void);                    // 按键状态检测
```

## 🛠️ 故障排除

### 常见问题

**Q1: 编译出现错误**
- 检查Keil工程配置是否正确
- 确认标准库路径设置
- 检查芯片型号选择

**Q2: 下载程序失败**
- 检查ST-Link连接
- 确认芯片供电正常
- 尝试擦除芯片后重新下载

**Q3: 按键无响应**
- 检查按键接线是否正确
- 确认按键另一端接地
- 测量按键电压是否正常

**Q4: 电机不转动**
- 检查电机供电电压
- 确认PWM信号输出
- 检查电机驱动电路

**Q5: 电机转向错误**
- 调换电机正负极接线
- 或修改代码中方向控制逻辑

### 调试方法
1. **LED指示**：可添加LED指示当前状态
2. **串口调试**：可启用串口输出调试信息
3. **示波器**：检测PWM波形是否正常
4. **万用表**：测量各关键点电压

## 🔄 功能扩展

### 建议扩展功能
1. **速度调节**：增加速度档位切换
2. **方向控制**：支持正转/反转切换  
3. **无线控制**：集成蓝牙/WiFi模块
4. **位置反馈**：添加编码器检测
5. **自动控制**：恢复循迹功能
6. **显示界面**：重新启用OLED显示

### 扩展接口
```c
// 预留扩展接口
void SetMotorSpeed(int leftSpeed, int rightSpeed);  // 双轮速度设置
void SetMotorDirection(int direction);              // 运动方向设置
int GetKeyState(void);                              // 获取按键状态
```

## 📞 技术支持

如遇到技术问题，请检查：
1. 硬件连接是否正确
2. 电源供电是否稳定
3. 代码编译是否成功
4. 程序下载是否完整

## 💻 示例代码

### 主程序流程
```c
int main(void)
{
    // 系统初始化
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    Motor_Init();    // 电机初始化
    Key_Init();      // 按键初始化

    while (1)
    {
        Key_GetNum();    // 获取按键状态

        // 左前轮控制逻辑
        if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_4) == 0)
            LeftWheelFront_Speed(80);     // 按下时转动
        else
            LeftWheelFront_Speed(0);      // 松开时停止

        // 右前轮控制逻辑
        if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_5) == 0)
            RightWheelFront_Speed(80);    // 按下时转动
        else
            RightWheelFront_Speed(0);     // 松开时停止

        Delay_ms(10);    // 10ms循环周期
    }
}
```

### 电机控制函数
```c
// 左前轮速度控制 (-100~100)
void LeftWheelFront_Speed(int Speed)
{
    if(Speed > 0) {
        GPIO_SetBits(GPIOB, GPIO_Pin_0);      // 正转
        GPIO_ResetBits(GPIOB, GPIO_Pin_1);
        TIM_SetCompare1(TIM2, Speed);
    }
    else if(Speed < 0) {
        GPIO_SetBits(GPIOB, GPIO_Pin_1);      // 反转
        GPIO_ResetBits(GPIOB, GPIO_Pin_0);
        TIM_SetCompare1(TIM2, -Speed);
    }
    else {
        TIM_SetCompare1(TIM2, 0);             // 停止
    }
}
```

## 📊 性能指标

### 响应时间
- **按键检测周期**：10ms
- **PWM更新频率**：20kHz
- **电机启动时间**：<50ms
- **电机停止时间**：<100ms

### 功耗参数
- **待机功耗**：约20mA@3.3V
- **单轮运行**：约200mA@5V
- **双轮运行**：约400mA@5V
- **建议电源**：5V/1A以上

## 🔍 测试验证

### 功能测试清单
- [ ] 编译无错误无警告
- [ ] 程序下载成功
- [ ] 系统正常启动
- [ ] 按键PA4控制左前轮
- [ ] 按键PA5控制右前轮
- [ ] 松开按键电机停止
- [ ] 双键同按前进运动
- [ ] 长时间运行稳定

### 性能测试
1. **响应测试**：按键按下到电机转动的延时
2. **精度测试**：PWM占空比与实际转速对应关系
3. **稳定性测试**：连续运行1小时无异常
4. **温度测试**：电机和驱动芯片温升情况

---

**版本信息**
- 文档版本：v1.0
- 更新日期：2024-12-25
- 适用固件：STM32双轮控制v1.0
- 作者：STM32开发团队
