# 🔌 STM32双轮小车硬件接线图说明

## 📋 接线总览

### 🎯 核心连接
```
STM32F103C8T6 ←→ 电机驱动模块 ←→ 直流电机
STM32F103C8T6 ←→ 按键开关
电源适配器 ←→ STM32 + 电机驱动
```

## 🔧 详细接线表

### 📍 STM32引脚分配
| 引脚 | 功能 | 连接对象 | 说明 |
|------|------|----------|------|
| PA0 | TIM2_CH1 | 左前轮PWM | 左前轮速度控制 |
| PA2 | TIM2_CH3 | 右前轮PWM | 右前轮速度控制 |
| PA4 | GPIO_Input | Key1按键 | 左前轮控制按键 |
| PA5 | GPIO_Input | Key2按键 | 右前轮控制按键 |
| PB0 | GPIO_Output | 左前轮方向1 | 左前轮正转控制 |
| PB1 | GPIO_Output | 左前轮方向2 | 左前轮反转控制 |
| PB12 | GPIO_Output | 右前轮方向1 | 右前轮正转控制 |
| PB13 | GPIO_Output | 右前轮方向2 | 右前轮反转控制 |
| 3.3V | 电源正极 | 系统供电 | STM32工作电压 |
| GND | 电源负极 | 系统地线 | 公共地线 |

### 🔋 电源连接
```
5V电源适配器：
├── 正极(+) → 电机驱动模块VCC
├── 正极(+) → STM32开发板5V (可选)
└── 负极(-) → 公共GND

3.3V稳压：
├── STM32内部稳压器输出
└── 为STM32核心供电
```

### ⚙️ 电机驱动连接
```
左前轮电机驱动：
├── PWM输入 ← PA0 (TIM2_CH1)
├── 方向控制1 ← PB0
├── 方向控制2 ← PB1
├── 电机输出A → 左前轮电机正极
├── 电机输出B → 左前轮电机负极
├── VCC ← 5V电源
└── GND ← 公共地线

右前轮电机驱动：
├── PWM输入 ← PA2 (TIM2_CH3)
├── 方向控制1 ← PB12
├── 方向控制2 ← PB13
├── 电机输出A → 右前轮电机正极
├── 电机输出B → 右前轮电机负极
├── VCC ← 5V电源
└── GND ← 公共地线
```

### 🎮 按键连接
```
Key1 (左前轮控制)：
├── 一端 → PA4
└── 另一端 → GND

Key2 (右前轮控制)：
├── 一端 → PA5
└── 另一端 → GND

注意：STM32内部已配置上拉电阻
```

## 🔍 接线步骤

### 第1步：准备工作
1. **断开所有电源**
2. **准备接线工具**：杜邦线、面包板等
3. **检查元件**：确认所有元件完好
4. **准备万用表**：用于测试连接

### 第2步：电源接线
```
⚠️ 重要：先接地线，后接电源线

1. 连接公共地线：
   GND → STM32 GND
   GND → 电机驱动 GND
   GND → 按键公共端

2. 连接电源线：
   5V → 电机驱动 VCC
   3.3V → STM32 VCC (如果需要)
```

### 第3步：信号线接线
```
1. PWM信号线：
   STM32 PA0 → 左前轮驱动PWM输入
   STM32 PA2 → 右前轮驱动PWM输入

2. 方向控制线：
   STM32 PB0 → 左前轮驱动IN1
   STM32 PB1 → 左前轮驱动IN2
   STM32 PB12 → 右前轮驱动IN1
   STM32 PB13 → 右前轮驱动IN2

3. 按键信号线：
   STM32 PA4 → Key1一端
   STM32 PA5 → Key2一端
```

### 第4步：电机连接
```
1. 左前轮电机：
   电机正极 → 左驱动输出A
   电机负极 → 左驱动输出B

2. 右前轮电机：
   电机正极 → 右驱动输出A
   电机负极 → 右驱动输出B

注意：如果转向相反，调换正负极即可
```

## 🧪 接线测试

### 🔍 测试清单
- [ ] **电源测试**：用万用表测量各点电压
- [ ] **地线测试**：确认所有GND连通
- [ ] **信号测试**：检查PWM和GPIO信号
- [ ] **按键测试**：按下时电压应为0V
- [ ] **电机测试**：手动给电机加电测试

### 📊 电压参考值
| 测试点 | 正常电压 | 说明 |
|--------|----------|------|
| STM32 VCC | 3.3V | STM32工作电压 |
| 电机驱动VCC | 5.0V | 电机驱动电压 |
| PA4/PA5未按下 | 3.3V | 上拉电阻作用 |
| PA4/PA5按下 | 0V | 按键接地 |
| PWM输出 | 0-3.3V | 方波信号 |

## ⚠️ 安全注意事项

### 🚨 接线安全
1. **断电操作**：接线时必须断开所有电源
2. **极性正确**：注意电源正负极，避免反接
3. **短路防护**：避免信号线短路
4. **绝缘良好**：确保接线牢固，绝缘良好

### 🔒 电气安全
1. **电压匹配**：确认各模块电压等级匹配
2. **电流限制**：电机电流不超过驱动能力
3. **散热考虑**：大电流时注意散热
4. **过流保护**：建议添加保险丝

### 🛡️ 操作安全
1. **首次测试**：小车架空测试
2. **逐步验证**：分模块测试后整体测试
3. **应急停止**：准备紧急断电开关
4. **环境安全**：确保测试环境安全

## 🔧 故障排除

### 🚫 常见接线错误
| 故障现象 | 可能原因 | 解决方法 |
|----------|----------|----------|
| 电机不转 | PWM线未连接 | 检查PA0/PA2连接 |
| 转向相反 | 电机极性接反 | 调换电机正负极 |
| 按键无效 | 按键线路错误 | 检查PA4/PA5接地 |
| 系统重启 | 电源不稳定 | 检查电源容量 |
| 发热严重 | 短路或过载 | 检查接线和负载 |

### 🔍 调试方法
1. **分段测试**：逐个模块测试
2. **信号追踪**：用示波器查看波形
3. **电压测量**：万用表测试各点电压
4. **电流监测**：测量工作电流是否正常

## 📐 PCB布局建议

### 🎯 布局原则
1. **电源分离**：数字电源与电机电源分离
2. **地线加粗**：大电流地线使用粗线
3. **信号隔离**：PWM信号远离敏感电路
4. **散热考虑**：大功率器件预留散热空间

### 🔌 连接器建议
- **电机连接**：使用大电流端子
- **信号连接**：使用标准杜邦头
- **电源连接**：使用防反插连接器
- **调试接口**：预留SWD调试接口

---

**✅ 接线完成后，请参考《快速入门指南.md》进行功能测试！**
