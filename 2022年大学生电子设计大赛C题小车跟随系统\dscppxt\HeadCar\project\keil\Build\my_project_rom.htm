<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Build\my_project_rom.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Build\my_project_rom.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 5060750: Last Updated: Sat May 20 03:11:55 2023
<BR><P>
<H3>Maximum Stack Usage =        148 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
main &rArr; uart_init &rArr; eusci_calcBaudDividers &rArr; CS_getSMCLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
 <LI><a href="#[6]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SVC_Handler</a><BR>
 <LI><a href="#[7]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">DebugMon_Handler</a><BR>
 <LI><a href="#[8]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">PendSV_Handler</a><BR>
 <LI><a href="#[9]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">SysTick_Handler</a><BR>
 <LI><a href="#[26]">AES256_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[26]">AES256_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[22]">ADC14_IRQHandler</a> from adc.o(i.ADC14_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[26]">AES256_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[10]">COMP_E0_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[11]">COMP_E1_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[b]">CS_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[28]">DMA_ERR_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[2c]">DMA_INT0_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[2b]">DMA_INT1_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[2a]">DMA_INT2_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[29]">DMA_INT3_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[1a]">EUSCIA0_IRQHandler</a> from main.o(i.EUSCIA0_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[1b]">EUSCIA1_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[1c]">EUSCIA2_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[1d]">EUSCIA3_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[1e]">EUSCIB0_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[1f]">EUSCIB1_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[20]">EUSCIB2_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[21]">EUSCIB3_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[f]">FLCTL_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[e]">FPU_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[c]">PCM_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[2d]">PORT1_IRQHandler</a> from key.o(i.PORT1_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[2e]">PORT2_IRQHandler</a> from encoder.o(i.PORT2_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[2f]">PORT3_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[30]">PORT4_IRQHandler</a> from encoder.o(i.PORT4_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[31]">PORT5_IRQHandler</a> from encoder.o(i.PORT5_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[32]">PORT6_IRQHandler</a> from encoder.o(i.PORT6_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[a]">PSS_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[27]">RTC_C_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[34]">SystemInit</a> from system_msp432p401r.o(i.SystemInit) referenced from startup_msp432p401r_uvision.o(.text)
 <LI><a href="#[23]">T32_INT1_IRQHandler</a> from tim32.o(i.T32_INT1_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[24]">T32_INT2_IRQHandler</a> from tim32.o(i.T32_INT2_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[25]">T32_INTC_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[12]">TA0_0_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[13]">TA0_N_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[14]">TA1_0_IRQHandler</a> from main.o(i.TA1_0_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[15]">TA1_N_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[16]">TA2_0_IRQHandler</a> from main.o(i.TA2_0_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[17]">TA2_N_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[18]">TA3_0_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[19]">TA3_N_IRQHandler</a> from tima.o(i.TA3_N_IRQHandler) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[d]">WDT_A_IRQHandler</a> from startup_msp432p401r_uvision.o(.text) referenced from startup_msp432p401r_uvision.o(RESET)
 <LI><a href="#[35]">__main</a> from entry.o(.ARM.Collect$$$$00000000) referenced from startup_msp432p401r_uvision.o(.text)
 <LI><a href="#[36]">fputc</a> from usart.o(i.fputc) referenced from printf1.o(i.__0printf$1)
 <LI><a href="#[33]">main</a> from main.o(i.main) referenced from entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[35]"></a>__main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry.o(.ARM.Collect$$$$00000000))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(.text)
</UL>
<P><STRONG><a name="[9f]"></a>_main_stk</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry2.o(.ARM.Collect$$$$00000001))

<P><STRONG><a name="[37]"></a>_main_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[44]"></a>__main_after_scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry5.o(.ARM.Collect$$$$00000004))
<BR><BR>[Called By]<UL><LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>

<P><STRONG><a name="[a0]"></a>_main_clock</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry7b.o(.ARM.Collect$$$$00000008))

<P><STRONG><a name="[a1]"></a>_main_cpp_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry8b.o(.ARM.Collect$$$$0000000A))

<P><STRONG><a name="[a2]"></a>_main_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry9a.o(.ARM.Collect$$$$0000000B))

<P><STRONG><a name="[a3]"></a>__rt_final_cpp</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry10a.o(.ARM.Collect$$$$0000000D))

<P><STRONG><a name="[a4]"></a>__rt_final_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, entry11a.o(.ARM.Collect$$$$0000000F))

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[26]"></a>AES256_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AES256_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AES256_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>COMP_E0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>COMP_E1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>CS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>DMA_ERR_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>DMA_INT0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>DMA_INT1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>DMA_INT2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>DMA_INT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>EUSCIA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>EUSCIA2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>EUSCIA3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>EUSCIB0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>EUSCIB1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>EUSCIB2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EUSCIB3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>FLCTL_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FPU_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>PCM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2f]"></a>PORT3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>PSS_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>RTC_C_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>T32_INTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>TA0_0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TA0_N_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TA1_N_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TA2_N_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TA3_0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>WDT_A_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_msp432p401r_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[a5]"></a>__aeabi_memcpy</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[65]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Configuration
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimA3_Cap_Init
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimA0_PWM_Init
</UL>

<P><STRONG><a name="[a6]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, memcpya.o(.text), UNUSED)

<P><STRONG><a name="[39]"></a>__aeabi_dadd</STRONG> (Thumb, 322 bytes, Stack size 48 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_lasr
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dsub
</UL>

<P><STRONG><a name="[3e]"></a>__aeabi_dsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[3f]"></a>__aeabi_drsub</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, dadd.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
</UL>

<P><STRONG><a name="[40]"></a>__aeabi_dmul</STRONG> (Thumb, 228 bytes, Stack size 48 bytes, dmul.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = __aeabi_dmul &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
</UL>

<P><STRONG><a name="[41]"></a>__aeabi_i2d</STRONG> (Thumb, 34 bytes, Stack size 16 bytes, dflti.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = __aeabi_i2d &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
</UL>

<P><STRONG><a name="[42]"></a>__aeabi_d2iz</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, dfixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_d2iz
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
<LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
</UL>

<P><STRONG><a name="[a7]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)

<P><STRONG><a name="[8b]"></a>__aeabi_uidivmod</STRONG> (Thumb, 44 bytes, Stack size 12 bytes, uidiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[3a]"></a>__aeabi_llsl</STRONG> (Thumb, 30 bytes, Stack size 0 bytes, llshl.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[a8]"></a>_ll_shift_l</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llshl.o(.text), UNUSED)

<P><STRONG><a name="[43]"></a>__aeabi_llsr</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, llushr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[a9]"></a>_ll_ushift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llushr.o(.text), UNUSED)

<P><STRONG><a name="[3b]"></a>__aeabi_lasr</STRONG> (Thumb, 36 bytes, Stack size 0 bytes, llsshr.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[aa]"></a>_ll_sshift_r</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, llsshr.o(.text), UNUSED)

<P><STRONG><a name="[ab]"></a>__I$use$fp</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, iusefp.o(.text), UNUSED)

<P><STRONG><a name="[3d]"></a>_double_round</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = _double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_epilogue
</UL>

<P><STRONG><a name="[3c]"></a>_double_epilogue</STRONG> (Thumb, 156 bytes, Stack size 32 bytes, depilogue.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsr
<LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_llsl
<LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_double_round
</UL>
<BR>[Called By]<UL><LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
</UL>

<P><STRONG><a name="[38]"></a>__scatterload</STRONG> (Thumb, 28 bytes, Stack size 0 bytes, init.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main_after_scatterload
</UL>
<BR>[Called By]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_main_scatterload
</UL>

<P><STRONG><a name="[ac]"></a>__scatterload_rt2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, init.o(.text), UNUSED)

<P><STRONG><a name="[22]"></a>ADC14_IRQHandler</STRONG> (Thumb, 66 bytes, Stack size 16 bytes, adc.o(i.ADC14_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = ADC14_IRQHandler &rArr; __2printf
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC14_getMultiSequenceResult
<LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__2printf
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>ADC14_getMultiSequenceResult</STRONG> (Thumb, 38 bytes, Stack size 8 bytes, adc14.o(i.ADC14_getMultiSequenceResult))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC14_getMultiSequenceResult
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC14_IRQHandler
</UL>

<P><STRONG><a name="[97]"></a>BEEP_Init</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, led.o(i.BEEP_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = BEEP_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[95]"></a>BEEP_Off</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, led.o(i.BEEP_Off))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[59]"></a>BEEP_On</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, led.o(i.BEEP_On))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSM
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[88]"></a>CS_clearInterruptFlag</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, cs.o(i.CS_clearInterruptFlag))
<BR><BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_CSComputeCLKFrequency
</UL>

<P><STRONG><a name="[47]"></a>CS_getACLK</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, cs.o(i.CS_getACLK))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = CS_getACLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_CSComputeCLKFrequency
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eusci_calcBaudDividers
</UL>

<P><STRONG><a name="[49]"></a>CS_getDCOFrequency</STRONG> (Thumb, 234 bytes, Stack size 16 bytes, cs.o(i.CS_getDCOFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtl_A_getTLVInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_CSComputeCLKFrequency
</UL>

<P><STRONG><a name="[4b]"></a>CS_getMCLK</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, cs.o(i.CS_getMCLK))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = CS_getMCLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_CSComputeCLKFrequency
</UL>
<BR>[Called By]<UL><LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
</UL>

<P><STRONG><a name="[4c]"></a>CS_getSMCLK</STRONG> (Thumb, 18 bytes, Stack size 0 bytes, cs.o(i.CS_getSMCLK))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = CS_getSMCLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_CSComputeCLKFrequency
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eusci_calcBaudDividers
</UL>

<P><STRONG><a name="[82]"></a>CS_setExternalClockSourceFrequency</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, cs.o(i.CS_setExternalClockSourceFrequency))
<BR><BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
</UL>

<P><STRONG><a name="[4d]"></a>CS_startHFXT</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cs.o(i.CS_startHFXT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CS_startHFXT &rArr; CS_startHFXTWithTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startHFXTWithTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
</UL>

<P><STRONG><a name="[4e]"></a>CS_startHFXTWithTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, cs.o(i.CS_startHFXTWithTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CS_startHFXTWithTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtl_A_getNMISourceStatus
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtl_A_enableNMISource
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtl_A_disableNMISource
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_CSGetHFXTFrequency
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startHFXT
</UL>

<P><STRONG><a name="[53]"></a>CS_startLFXT</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, cs.o(i.CS_startLFXT))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CS_startLFXT &rArr; CS_startLFXTWithTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startLFXTWithTimeout
</UL>
<BR>[Called By]<UL><LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
</UL>

<P><STRONG><a name="[54]"></a>CS_startLFXTWithTimeout</STRONG> (Thumb, 144 bytes, Stack size 32 bytes, cs.o(i.CS_startLFXTWithTimeout))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = CS_startLFXTWithTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtl_A_getNMISourceStatus
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtl_A_enableNMISource
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysCtl_A_disableNMISource
</UL>
<BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startLFXT
</UL>

<P><STRONG><a name="[1a]"></a>EUSCIA0_IRQHandler</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, main.o(i.EUSCIA0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = EUSCIA0_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_receiveData
<LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_clearInterruptFlag
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[57]"></a>FSM</STRONG> (Thumb, 530 bytes, Stack size 40 bytes, fsm.o(i.FSM))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = FSM &rArr; Grayscale_Judge
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_R_Tog
<LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_RED_Tog
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_On
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Outer_Ring
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Inner_Ring
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Grayscale_Judge
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
</UL>

<P><STRONG><a name="[7a]"></a>GPIO_clearInterruptFlag</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gpio.o(i.GPIO_clearInterruptFlag))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT6_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT5_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT4_IRQHandler
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT2_IRQHandler
</UL>

<P><STRONG><a name="[7c]"></a>GPIO_enableInterrupt</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gpio.o(i.GPIO_enableInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_Encoder_Init
</UL>

<P><STRONG><a name="[5e]"></a>GPIO_getEnabledInterruptStatus</STRONG> (Thumb, 52 bytes, Stack size 4 bytes, gpio.o(i.GPIO_getEnabledInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = GPIO_getEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT6_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT5_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT4_IRQHandler
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT2_IRQHandler
</UL>

<P><STRONG><a name="[63]"></a>GPIO_getInputPinValue</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, gpio.o(i.GPIO_getInputPinValue))
<BR><BR>[Called By]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Grayscale_scan
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT6_IRQHandler
<LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT5_IRQHandler
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT4_IRQHandler
<LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT2_IRQHandler
</UL>

<P><STRONG><a name="[5f]"></a>GPIO_getInterruptStatus</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, gpio.o(i.GPIO_getInterruptStatus))
<BR><BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getEnabledInterruptStatus
</UL>

<P><STRONG><a name="[7e]"></a>GPIO_interruptEdgeSelect</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, gpio.o(i.GPIO_interruptEdgeSelect))
<BR><BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_Encoder_Init
</UL>

<P><STRONG><a name="[61]"></a>GPIO_setAsInputPin</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, gpio.o(i.GPIO_setAsInputPin))
<BR><BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Grayscale_Init
</UL>

<P><STRONG><a name="[6a]"></a>GPIO_setAsOutputPin</STRONG> (Thumb, 26 bytes, Stack size 0 bytes, gpio.o(i.GPIO_setAsOutputPin))
<BR><BR>[Called By]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimA3_Cap_Init
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
</UL>

<P><STRONG><a name="[6f]"></a>GPIO_setOutputHighOnPin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gpio.o(i.GPIO_setOutputHighOnPin))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ult_start
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Set
</UL>

<P><STRONG><a name="[70]"></a>GPIO_setOutputLowOnPin</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, gpio.o(i.GPIO_setOutputLowOnPin))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ult_start
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Set
</UL>

<P><STRONG><a name="[60]"></a>Grayscale_Init</STRONG> (Thumb, 70 bytes, Stack size 8 bytes, grayscale.o(i.Grayscale_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Grayscale_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_setAsInputPin
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[58]"></a>Grayscale_Judge</STRONG> (Thumb, 618 bytes, Stack size 48 bytes, grayscale.o(i.Grayscale_Judge))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Grayscale_Judge
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSM
</UL>

<P><STRONG><a name="[62]"></a>Grayscale_scan</STRONG> (Thumb, 118 bytes, Stack size 8 bytes, grayscale.o(i.Grayscale_scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Grayscale_scan
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getInputPinValue
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
</UL>

<P><STRONG><a name="[64]"></a>I2C_Configuration</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, oled.o(i.I2C_Configuration))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = I2C_Configuration
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[79]"></a>I2C_masterSendMultiByteFinish</STRONG> (Thumb, 52 bytes, Stack size 0 bytes, i2c.o(i.I2C_masterSendMultiByteFinish))
<BR><BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[78]"></a>I2C_masterSendMultiByteStart</STRONG> (Thumb, 52 bytes, Stack size 8 bytes, i2c.o(i.I2C_masterSendMultiByteStart))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = I2C_masterSendMultiByteStart
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>

<P><STRONG><a name="[6c]"></a>Incremental_PID_Calc</STRONG> (Thumb, 114 bytes, Stack size 0 bytes, pidcontrol.o(i.Incremental_PID_Calc))
<BR><BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_PID
</UL>

<P><STRONG><a name="[9b]"></a>Incremental_PID_Init</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, pidcontrol.o(i.Incremental_PID_Init))
<BR><BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[5d]"></a>Inner_Ring</STRONG> (Thumb, 206 bytes, Stack size 20 bytes, grayscale.o(i.Inner_Ring))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = Inner_Ring
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSM
</UL>

<P><STRONG><a name="[7d]"></a>Interrupt_enableInterrupt</STRONG> (Thumb, 84 bytes, Stack size 0 bytes, interrupt.o(i.Interrupt_enableInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_Encoder_Init
</UL>

<P><STRONG><a name="[9c]"></a>Interrupt_setPriority</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, interrupt.o(i.Interrupt_setPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Interrupt_setPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[98]"></a>KEY_Init</STRONG> (Thumb, 90 bytes, Stack size 16 bytes, key.o(i.KEY_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = KEY_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[66]"></a>KEY_Scan</STRONG> (Thumb, 74 bytes, Stack size 16 bytes, key.o(i.KEY_Scan))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = KEY_Scan &rArr; key_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[93]"></a>LED_B_Tog</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, led.o(i.LED_B_Tog))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[94]"></a>LED_G_Tog</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, led.o(i.LED_G_Tog))
<BR><BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>LED_Init</STRONG> (Thumb, 46 bytes, Stack size 8 bytes, led.o(i.LED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = LED_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[5a]"></a>LED_RED_Tog</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, led.o(i.LED_RED_Tog))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSM
</UL>

<P><STRONG><a name="[5b]"></a>LED_R_Tog</STRONG> (Thumb, 14 bytes, Stack size 0 bytes, led.o(i.LED_R_Tog))
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSM
</UL>

<P><STRONG><a name="[68]"></a>Motor_Init</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, motor.o(i.Motor_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = Motor_Init &rArr; TimA0_PWM_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_setAsOutputPin
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimA0_PWM_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[6b]"></a>Motor_PID</STRONG> (Thumb, 94 bytes, Stack size 16 bytes, motor.o(i.Motor_PID))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = Motor_PID &rArr; Motor_Set
</UL>
<BR>[Calls]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Set
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PID_Calc
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA1_0_IRQHandler
</UL>

<P><STRONG><a name="[6d]"></a>Motor_Set</STRONG> (Thumb, 130 bytes, Stack size 24 bytes, motor.o(i.Motor_Set))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Motor_Set
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;myabs
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_setOutputLowOnPin
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_setOutputHighOnPin
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_PID
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA1_0_IRQHandler
</UL>

<P><STRONG><a name="[71]"></a>OLED_Clear</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, oled.o(i.OLED_Clear))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = OLED_Clear &rArr; OLED_WR_Byte &rArr; I2C_masterSendMultiByteStart
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[73]"></a>OLED_Init</STRONG> (Thumb, 238 bytes, Stack size 8 bytes, oled.o(i.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = OLED_Init &rArr; OLED_Clear &rArr; OLED_WR_Byte &rArr; I2C_masterSendMultiByteStart
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_Configuration
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[74]"></a>OLED_Set_Pos</STRONG> (Thumb, 40 bytes, Stack size 8 bytes, oled.o(i.OLED_Set_Pos))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = OLED_Set_Pos &rArr; OLED_WR_Byte &rArr; I2C_masterSendMultiByteStart
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>

<P><STRONG><a name="[75]"></a>OLED_ShowChar</STRONG> (Thumb, 128 bytes, Stack size 32 bytes, oled.o(i.OLED_ShowChar))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = OLED_ShowChar &rArr; OLED_Set_Pos &rArr; OLED_WR_Byte &rArr; I2C_masterSendMultiByteStart
</UL>
<BR>[Calls]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WR_Byte
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
</UL>
<BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>

<P><STRONG><a name="[76]"></a>OLED_ShowNum</STRONG> (Thumb, 122 bytes, Stack size 40 bytes, oled.o(i.OLED_ShowNum))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_ShowNum &rArr; OLED_ShowChar &rArr; OLED_Set_Pos &rArr; OLED_WR_Byte &rArr; I2C_masterSendMultiByteStart
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;oled_pow
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[72]"></a>OLED_WR_Byte</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, oled.o(i.OLED_WR_Byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = OLED_WR_Byte &rArr; I2C_masterSendMultiByteStart
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_masterSendMultiByteStart
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;I2C_masterSendMultiByteFinish
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowChar
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Set_Pos
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Clear
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[5c]"></a>Outer_Ring</STRONG> (Thumb, 200 bytes, Stack size 32 bytes, grayscale.o(i.Outer_Ring))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = Outer_Ring
</UL>
<BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSM
</UL>

<P><STRONG><a name="[2d]"></a>PORT1_IRQHandler</STRONG> (Thumb, 46 bytes, Stack size 16 bytes, key.o(i.PORT1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = PORT1_IRQHandler &rArr; key_delay
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;key_delay
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>PORT2_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, encoder.o(i.PORT2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = PORT2_IRQHandler &rArr; GPIO_getEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_clearInterruptFlag
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getInputPinValue
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>PORT4_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, encoder.o(i.PORT4_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = PORT4_IRQHandler &rArr; GPIO_getEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_clearInterruptFlag
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getInputPinValue
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>PORT5_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, encoder.o(i.PORT5_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = PORT5_IRQHandler &rArr; GPIO_getEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_clearInterruptFlag
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getInputPinValue
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>PORT6_IRQHandler</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, encoder.o(i.PORT6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = PORT6_IRQHandler &rArr; GPIO_getEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_clearInterruptFlag
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getInputPinValue
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_getEnabledInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[80]"></a>SW_Encoder_Clear</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, encoder.o(i.SW_Encoder_Clear))
<BR><BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_Encoder_Read
</UL>

<P><STRONG><a name="[7b]"></a>SW_Encoder_Init</STRONG> (Thumb, 256 bytes, Stack size 8 bytes, encoder.o(i.SW_Encoder_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SW_Encoder_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_interruptEdgeSelect
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_enableInterrupt
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Interrupt_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[7f]"></a>SW_Encoder_Read</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, encoder.o(i.SW_Encoder_Read))
<BR><BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_Encoder_Clear
</UL>
<BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA1_0_IRQHandler
</UL>

<P><STRONG><a name="[50]"></a>SysCtl_A_disableNMISource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, sysctl_a.o(i.SysCtl_A_disableNMISource))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startLFXTWithTimeout
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startHFXTWithTimeout
</UL>

<P><STRONG><a name="[52]"></a>SysCtl_A_enableNMISource</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, sysctl_a.o(i.SysCtl_A_enableNMISource))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startLFXTWithTimeout
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startHFXTWithTimeout
</UL>

<P><STRONG><a name="[4f]"></a>SysCtl_A_getNMISourceStatus</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, sysctl_a.o(i.SysCtl_A_getNMISourceStatus))
<BR><BR>[Called By]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startLFXTWithTimeout
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startHFXTWithTimeout
</UL>

<P><STRONG><a name="[4a]"></a>SysCtl_A_getTLVInfo</STRONG> (Thumb, 64 bytes, Stack size 20 bytes, sysctl_a.o(i.SysCtl_A_getTLVInfo))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = SysCtl_A_getTLVInfo
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getDCOFrequency
</UL>

<P><STRONG><a name="[81]"></a>SysInit</STRONG> (Thumb, 126 bytes, Stack size 8 bytes, sysinit.o(i.SysInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = SysInit &rArr; CS_startLFXT &rArr; CS_startLFXTWithTimeout
</UL>
<BR>[Calls]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startLFXT
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startHFXT
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_setExternalClockSourceFrequency
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[34]"></a>SystemInit</STRONG> (Thumb, 76 bytes, Stack size 0 bytes, system_msp432p401r.o(i.SystemInit))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(.text)
</UL>
<P><STRONG><a name="[23]"></a>T32_INT1_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, tim32.o(i.T32_INT1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>T32_INT2_IRQHandler</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, tim32.o(i.T32_INT2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TA1_0_IRQHandler</STRONG> (Thumb, 156 bytes, Stack size 16 bytes, main.o(i.TA1_0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = TA1_0_IRQHandler &rArr; Motor_PID &rArr; Motor_Set
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;encoder_to_speed
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_Encoder_Read
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Set
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_PID
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TA2_0_IRQHandler</STRONG> (Thumb, 198 bytes, Stack size 32 bytes, main.o(i.TA2_0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = TA2_0_IRQHandler &rArr; __aeabi_drsub &rArr; __aeabi_dadd &rArr; _double_epilogue &rArr; _double_round
</UL>
<BR>[Calls]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ult_start
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2d
<LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_drsub
<LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dmul
<LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_dadd
<LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_transmitData
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Grayscale_scan
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FSM
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>TA3_N_IRQHandler</STRONG> (Thumb, 188 bytes, Stack size 24 bytes, tima.o(i.TA3_N_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = TA3_N_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_A_getCaptureCompareCount
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_msp432p401r_uvision.o(RESET)
</UL>
<P><STRONG><a name="[69]"></a>TimA0_PWM_Init</STRONG> (Thumb, 86 bytes, Stack size 40 bytes, tima.o(i.TimA0_PWM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TimA0_PWM_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
</UL>

<P><STRONG><a name="[9a]"></a>TimA1_Int_Init</STRONG> (Thumb, 94 bytes, Stack size 40 bytes, tima.o(i.TimA1_Int_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TimA1_Int_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[99]"></a>TimA2_Int_Init</STRONG> (Thumb, 76 bytes, Stack size 40 bytes, tima.o(i.TimA2_Int_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = TimA2_Int_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[87]"></a>TimA3_Cap_Init</STRONG> (Thumb, 118 bytes, Stack size 56 bytes, tima.o(i.TimA3_Cap_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = TimA3_Cap_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_setAsOutputPin
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>

<P><STRONG><a name="[86]"></a>Timer_A_getCaptureCompareCount</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timer_a.o(i.Timer_A_getCaptureCompareCount))
<BR><BR>[Called By]<UL><LI><a href="#[19]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA3_N_IRQHandler
</UL>

<P><STRONG><a name="[55]"></a>UART_clearInterruptFlag</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, uart.o(i.UART_clearInterruptFlag))
<BR><BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EUSCIA0_IRQHandler
</UL>

<P><STRONG><a name="[9e]"></a>UART_enableInterrupt</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, uart.o(i.UART_enableInterrupt))
<BR><BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[9d]"></a>UART_initModule</STRONG> (Thumb, 146 bytes, Stack size 16 bytes, uart.o(i.UART_initModule))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = UART_initModule
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[56]"></a>UART_receiveData</STRONG> (Thumb, 24 bytes, Stack size 0 bytes, uart.o(i.UART_receiveData))
<BR><BR>[Called By]<UL><LI><a href="#[1a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;EUSCIA0_IRQHandler
</UL>

<P><STRONG><a name="[85]"></a>UART_transmitData</STRONG> (Thumb, 22 bytes, Stack size 0 bytes, uart.o(i.UART_transmitData))
<BR><BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;fputc
</UL>

<P><STRONG><a name="[89]"></a>__0printf$1</STRONG> (Thumb, 22 bytes, Stack size 24 bytes, printf1.o(i.__0printf$1), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_printf_core
</UL>

<P><STRONG><a name="[ad]"></a>__1printf$1</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf1.o(i.__0printf$1), UNUSED)

<P><STRONG><a name="[46]"></a>__2printf</STRONG> (Thumb, 0 bytes, Stack size 24 bytes, printf1.o(i.__0printf$1))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __2printf
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC14_IRQHandler
</UL>

<P><STRONG><a name="[ae]"></a>__scatterload_copy</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_copy), UNUSED)

<P><STRONG><a name="[af]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_null), UNUSED)

<P><STRONG><a name="[b0]"></a>__scatterload_zeroinit</STRONG> (Thumb, 14 bytes, Stack size unknown bytes, handlers.o(i.__scatterload_zeroinit), UNUSED)

<P><STRONG><a name="[90]"></a>bitPosition</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, baudrate_calculate.o(i.bitPosition))
<BR><BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eusci_calcBaudDividers
</UL>

<P><STRONG><a name="[8c]"></a>delay_init</STRONG> (Thumb, 30 bytes, Stack size 8 bytes, delay.o(i.delay_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = delay_init &rArr; CS_getMCLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getMCLK
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8d]"></a>delay_ms</STRONG> (Thumb, 24 bytes, Stack size 12 bytes, delay.o(i.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = delay_ms
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8e]"></a>delay_us</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, delay.o(i.delay_us))
<BR><BR>[Called By]<UL><LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ult_start
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[83]"></a>encoder_to_speed</STRONG> (Thumb, 54 bytes, Stack size 0 bytes, encoder.o(i.encoder_to_speed))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA1_0_IRQHandler
</UL>

<P><STRONG><a name="[8f]"></a>eusci_calcBaudDividers</STRONG> (Thumb, 282 bytes, Stack size 32 bytes, baudrate_calculate.o(i.eusci_calcBaudDividers))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = eusci_calcBaudDividers &rArr; CS_getSMCLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getSMCLK
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getACLK
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bitPosition
</UL>
<BR>[Called By]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
</UL>

<P><STRONG><a name="[36]"></a>fputc</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, usart.o(i.fputc))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = fputc
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_transmitData
</UL>
<BR>[Address Reference Count : 1]<UL><LI> printf1.o(i.__0printf$1)
</UL>
<P><STRONG><a name="[33]"></a>main</STRONG> (Thumb, 228 bytes, Stack size 8 bytes, main.o(i.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 148<LI>Call Chain = main &rArr; uart_init &rArr; eusci_calcBaudDividers &rArr; CS_getSMCLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;uart_init
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_init
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysInit
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_G_Tog
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_B_Tog
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_On
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_Off
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;system_init
</UL>
<BR>[Address Reference Count : 1]<UL><LI> entry9a.o(.ARM.Collect$$$$0000000B)
</UL>
<P><STRONG><a name="[6e]"></a>myabs</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, globalvar.o(i.myabs))
<BR><BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Set
</UL>

<P><STRONG><a name="[77]"></a>oled_pow</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, oled.o(i.oled_pow))
<BR><BR>[Called By]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_ShowNum
</UL>

<P><STRONG><a name="[92]"></a>system_init</STRONG> (Thumb, 308 bytes, Stack size 24 bytes, main.o(i.system_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = system_init &rArr; TimA3_Cap_Init
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_d2iz
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimA3_Cap_Init
<LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimA2_Int_Init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TimA1_Int_Init
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SW_Encoder_Init
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_Init
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LED_Init
<LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Init
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Interrupt_setPriority
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Incremental_PID_Init
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Grayscale_Init
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_clearInterruptFlag
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BEEP_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[91]"></a>uart_init</STRONG> (Thumb, 78 bytes, Stack size 48 bytes, usart.o(i.uart_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = uart_init &rArr; eusci_calcBaudDividers &rArr; CS_getSMCLK &rArr; _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;eusci_calcBaudDividers
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_initModule
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART_enableInterrupt
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Interrupt_enableInterrupt
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[84]"></a>ult_start</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, tima.o(i.ult_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ult_start
</UL>
<BR>[Calls]<UL><LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_setOutputLowOnPin
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GPIO_setOutputHighOnPin
</UL>
<BR>[Called By]<UL><LI><a href="#[16]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TA2_0_IRQHandler
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[67]"></a>key_delay</STRONG> (Thumb, 42 bytes, Stack size 8 bytes, key.o(i.key_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = key_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PORT1_IRQHandler
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;KEY_Scan
</UL>

<P><STRONG><a name="[48]"></a>_CSComputeCLKFrequency</STRONG> (Thumb, 176 bytes, Stack size 24 bytes, cs.o(i._CSComputeCLKFrequency))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = _CSComputeCLKFrequency &rArr; CS_getDCOFrequency &rArr; SysCtl_A_getTLVInfo
</UL>
<BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getDCOFrequency
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_clearInterruptFlag
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getSMCLK
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getACLK
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_getMCLK
</UL>

<P><STRONG><a name="[51]"></a>_CSGetHFXTFrequency</STRONG> (Thumb, 106 bytes, Stack size 0 bytes, cs.o(i._CSGetHFXTFrequency))
<BR><BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;CS_startHFXTWithTimeout
</UL>

<P><STRONG><a name="[8a]"></a>_printf_core</STRONG> (Thumb, 336 bytes, Stack size 88 bytes, printf1.o(i._printf_core), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidivmod
</UL>
<BR>[Called By]<UL><LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__0printf$1
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
