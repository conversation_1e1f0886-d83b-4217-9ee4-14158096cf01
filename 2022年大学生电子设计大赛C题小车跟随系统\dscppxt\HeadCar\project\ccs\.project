<?xml version="1.0" encoding="UTF-8"?>
<projectDescription>
	<name>RNA_Empty(traditional)</name>
	<comment></comment>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>com.ti.ccstudio.core.ccsNature</nature>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.core.ccnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>hardware</name>
			<type>2</type>
			<locationURI>PARENT-2-PROJECT_LOC/hardware</locationURI>
		</link>
		<link>
			<name>main.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/main.c</locationURI>
		</link>
		<link>
			<name>sys</name>
			<type>2</type>
			<locationURI>PARENT-2-PROJECT_LOC/sys</locationURI>
		</link>
		<link>
			<name>system_msp432p401r.c</name>
			<type>1</type>
			<locationURI>PARENT-2-PROJECT_LOC/system_msp432p401r.c</locationURI>
		</link>
	</linkedResources>
</projectDescription>
