


ARM Macro Assembler    Page 1 


    1 00000000         ;******************** (C) COPYRIGHT 2011 STMicroelectron
                       ics ********************
    2 00000000         ;* File Name          : startup_stm32f10x_md.s
    3 00000000         ;* Author             : MCD Application Team
    4 00000000         ;* Version            : V3.5.0
    5 00000000         ;* Date               : 11-March-2011
    6 00000000         ;* Description        : STM32F10x Medium Density Devices
                        vector table for MDK-ARM 
    7 00000000         ;*                      toolchain.  
    8 00000000         ;*                      This module performs:
    9 00000000         ;*                      - Set the initial SP
   10 00000000         ;*                      - Set the initial PC == Reset_Ha
                       ndler
   11 00000000         ;*                      - Set the vector table entries w
                       ith the exceptions ISR address
   12 00000000         ;*                      - Configure the clock system
   13 00000000         ;*                      - Branches to __main in the C li
                       brary (which eventually
   14 00000000         ;*                        calls main()).
   15 00000000         ;*                      After Reset the CortexM3 process
                       or is in Thread mode,
   16 00000000         ;*                      priority is Privileged, and the 
                       Stack is set to Main.
   17 00000000         ;* <<< Use Configuration Wizard in Context Menu >>>   
   18 00000000         ;*******************************************************
                       ************************
   19 00000000         ; THE PRESENT FIRMWARE WHICH IS FOR GUIDANCE ONLY AIMS A
                       T PROVIDING CUSTOMERS
   20 00000000         ; WITH CODING INFORMATION REGARDING THEIR PRODUCTS IN OR
                       DER FOR THEM TO SAVE TIME.
   21 00000000         ; AS A RESULT, STMICROELECTRONICS SHALL NOT BE HELD LIAB
                       LE FOR ANY DIRECT,
   22 00000000         ; INDIRECT OR CONSEQUENTIAL DAMAGES WITH RESPECT TO ANY 
                       CLAIMS ARISING FROM THE
   23 00000000         ; CONTENT OF SUCH FIRMWARE AND/OR THE USE MADE BY CUSTOM
                       ERS OF THE CODING
   24 00000000         ; INFORMATION CONTAINED HEREIN IN CONNECTION WITH THEIR 
                       PRODUCTS.
   25 00000000         ;*******************************************************
                       ************************
   26 00000000         
   27 00000000         ; Amount of memory (in bytes) allocated for Stack
   28 00000000         ; Tailor this value to your application needs
   29 00000000         ; <h> Stack Configuration
   30 00000000         ;   <o> Stack Size (in Bytes) <0x0-0xFFFFFFFF:8>
   31 00000000         ; </h>
   32 00000000         
   33 00000000 00000400 
                       Stack_Size
                               EQU              0x00000400
   34 00000000         
   35 00000000                 AREA             STACK, NOINIT, READWRITE, ALIGN
=3
   36 00000000         Stack_Mem
                               SPACE            Stack_Size
   37 00000400         __initial_sp
   38 00000400         
   39 00000400         
   40 00000400         ; <h> Heap Configuration



ARM Macro Assembler    Page 2 


   41 00000400         ;   <o>  Heap Size (in Bytes) <0x0-0xFFFFFFFF:8>
   42 00000400         ; </h>
   43 00000400         
   44 00000400 00000200 
                       Heap_Size
                               EQU              0x00000200
   45 00000400         
   46 00000400                 AREA             HEAP, NOINIT, READWRITE, ALIGN=
3
   47 00000000         __heap_base
   48 00000000         Heap_Mem
                               SPACE            Heap_Size
   49 00000200         __heap_limit
   50 00000200         
   51 00000200                 PRESERVE8
   52 00000200                 THUMB
   53 00000200         
   54 00000200         
   55 00000200         ; Vector Table Mapped to Address 0 at Reset
   56 00000200                 AREA             RESET, DATA, READONLY
   57 00000000                 EXPORT           __Vectors
   58 00000000                 EXPORT           __Vectors_End
   59 00000000                 EXPORT           __Vectors_Size
   60 00000000         
   61 00000000 00000000 
                       __Vectors
                               DCD              __initial_sp ; Top of Stack
   62 00000004 00000000        DCD              Reset_Handler ; Reset Handler
   63 00000008 00000000        DCD              NMI_Handler ; NMI Handler
   64 0000000C 00000000        DCD              HardFault_Handler ; Hard Fault 
                                                            Handler
   65 00000010 00000000        DCD              MemManage_Handler 
                                                            ; MPU Fault Handler
                                                            
   66 00000014 00000000        DCD              BusFault_Handler 
                                                            ; Bus Fault Handler
                                                            
   67 00000018 00000000        DCD              UsageFault_Handler ; Usage Faul
                                                            t Handler
   68 0000001C 00000000        DCD              0           ; Reserved
   69 00000020 00000000        DCD              0           ; Reserved
   70 00000024 00000000        DCD              0           ; Reserved
   71 00000028 00000000        DCD              0           ; Reserved
   72 0000002C 00000000        DCD              SVC_Handler ; SVCall Handler
   73 00000030 00000000        DCD              DebugMon_Handler ; Debug Monito
                                                            r Handler
   74 00000034 00000000        DCD              0           ; Reserved
   75 00000038 00000000        DCD              PendSV_Handler ; PendSV Handler
                                                            
   76 0000003C 00000000        DCD              SysTick_Handler 
                                                            ; SysTick Handler
   77 00000040         
   78 00000040         ; External Interrupts
   79 00000040 00000000        DCD              WWDG_IRQHandler 
                                                            ; Window Watchdog
   80 00000044 00000000        DCD              PVD_IRQHandler ; PVD through EX
                                                            TI Line detect
   81 00000048 00000000        DCD              TAMPER_IRQHandler ; Tamper
   82 0000004C 00000000        DCD              RTC_IRQHandler ; RTC



ARM Macro Assembler    Page 3 


   83 00000050 00000000        DCD              FLASH_IRQHandler ; Flash
   84 00000054 00000000        DCD              RCC_IRQHandler ; RCC
   85 00000058 00000000        DCD              EXTI0_IRQHandler ; EXTI Line 0
   86 0000005C 00000000        DCD              EXTI1_IRQHandler ; EXTI Line 1
   87 00000060 00000000        DCD              EXTI2_IRQHandler ; EXTI Line 2
   88 00000064 00000000        DCD              EXTI3_IRQHandler ; EXTI Line 3
   89 00000068 00000000        DCD              EXTI4_IRQHandler ; EXTI Line 4
   90 0000006C 00000000        DCD              DMA1_Channel1_IRQHandler 
                                                            ; DMA1 Channel 1
   91 00000070 00000000        DCD              DMA1_Channel2_IRQHandler 
                                                            ; DMA1 Channel 2
   92 00000074 00000000        DCD              DMA1_Channel3_IRQHandler 
                                                            ; DMA1 Channel 3
   93 00000078 00000000        DCD              DMA1_Channel4_IRQHandler 
                                                            ; DMA1 Channel 4
   94 0000007C 00000000        DCD              DMA1_Channel5_IRQHandler 
                                                            ; DMA1 Channel 5
   95 00000080 00000000        DCD              DMA1_Channel6_IRQHandler 
                                                            ; DMA1 Channel 6
   96 00000084 00000000        DCD              DMA1_Channel7_IRQHandler 
                                                            ; DMA1 Channel 7
   97 00000088 00000000        DCD              ADC1_2_IRQHandler ; ADC1_2
   98 0000008C 00000000        DCD              USB_HP_CAN1_TX_IRQHandler ; USB
                                                             High Priority or C
                                                            AN1 TX
   99 00000090 00000000        DCD              USB_LP_CAN1_RX0_IRQHandler ; US
                                                            B Low  Priority or 
                                                            CAN1 RX0
  100 00000094 00000000        DCD              CAN1_RX1_IRQHandler ; CAN1 RX1
  101 00000098 00000000        DCD              CAN1_SCE_IRQHandler ; CAN1 SCE
  102 0000009C 00000000        DCD              EXTI9_5_IRQHandler 
                                                            ; EXTI Line 9..5
  103 000000A0 00000000        DCD              TIM1_BRK_IRQHandler 
                                                            ; TIM1 Break
  104 000000A4 00000000        DCD              TIM1_UP_IRQHandler 
                                                            ; TIM1 Update
  105 000000A8 00000000        DCD              TIM1_TRG_COM_IRQHandler ; TIM1 
                                                            Trigger and Commuta
                                                            tion
  106 000000AC 00000000        DCD              TIM1_CC_IRQHandler ; TIM1 Captu
                                                            re Compare
  107 000000B0 00000000        DCD              TIM2_IRQHandler ; TIM2
  108 000000B4 00000000        DCD              TIM3_IRQHandler ; TIM3
  109 000000B8 00000000        DCD              TIM4_IRQHandler ; TIM4
  110 000000BC 00000000        DCD              I2C1_EV_IRQHandler ; I2C1 Event
                                                            
  111 000000C0 00000000        DCD              I2C1_ER_IRQHandler ; I2C1 Error
                                                            
  112 000000C4 00000000        DCD              I2C2_EV_IRQHandler ; I2C2 Event
                                                            
  113 000000C8 00000000        DCD              I2C2_ER_IRQHandler ; I2C2 Error
                                                            
  114 000000CC 00000000        DCD              SPI1_IRQHandler ; SPI1
  115 000000D0 00000000        DCD              SPI2_IRQHandler ; SPI2
  116 000000D4 00000000        DCD              USART1_IRQHandler ; USART1
  117 000000D8 00000000        DCD              USART2_IRQHandler ; USART2
  118 000000DC 00000000        DCD              USART3_IRQHandler ; USART3
  119 000000E0 00000000        DCD              EXTI15_10_IRQHandler 
                                                            ; EXTI Line 15..10



ARM Macro Assembler    Page 4 


  120 000000E4 00000000        DCD              RTCAlarm_IRQHandler ; RTC Alarm
                                                             through EXTI Line
  121 000000E8 00000000        DCD              USBWakeUp_IRQHandler ; USB Wake
                                                            up from suspend
  122 000000EC         __Vectors_End
  123 000000EC         
  124 000000EC 000000EC 
                       __Vectors_Size
                               EQU              __Vectors_End - __Vectors
  125 000000EC         
  126 000000EC                 AREA             |.text|, CODE, READONLY
  127 00000000         
  128 00000000         ; Reset handler
  129 00000000         Reset_Handler
                               PROC
  130 00000000                 EXPORT           Reset_Handler             [WEAK
]
  131 00000000                 IMPORT           __main
  132 00000000                 IMPORT           SystemInit
  133 00000000 4809            LDR              R0, =SystemInit
  134 00000002 4780            BLX              R0
  135 00000004 4809            LDR              R0, =__main
  136 00000006 4700            BX               R0
  137 00000008                 ENDP
  138 00000008         
  139 00000008         ; Dummy Exception Handlers (infinite loops which can be 
                       modified)
  140 00000008         
  141 00000008         NMI_Handler
                               PROC
  142 00000008                 EXPORT           NMI_Handler                [WEA
K]
  143 00000008 E7FE            B                .
  144 0000000A                 ENDP
  146 0000000A         HardFault_Handler
                               PROC
  147 0000000A                 EXPORT           HardFault_Handler          [WEA
K]
  148 0000000A E7FE            B                .
  149 0000000C                 ENDP
  151 0000000C         MemManage_Handler
                               PROC
  152 0000000C                 EXPORT           MemManage_Handler          [WEA
K]
  153 0000000C E7FE            B                .
  154 0000000E                 ENDP
  156 0000000E         BusFault_Handler
                               PROC
  157 0000000E                 EXPORT           BusFault_Handler           [WEA
K]
  158 0000000E E7FE            B                .
  159 00000010                 ENDP
  161 00000010         UsageFault_Handler
                               PROC
  162 00000010                 EXPORT           UsageFault_Handler         [WEA
K]
  163 00000010 E7FE            B                .
  164 00000012                 ENDP
  165 00000012         SVC_Handler



ARM Macro Assembler    Page 5 


                               PROC
  166 00000012                 EXPORT           SVC_Handler                [WEA
K]
  167 00000012 E7FE            B                .
  168 00000014                 ENDP
  170 00000014         DebugMon_Handler
                               PROC
  171 00000014                 EXPORT           DebugMon_Handler           [WEA
K]
  172 00000014 E7FE            B                .
  173 00000016                 ENDP
  174 00000016         PendSV_Handler
                               PROC
  175 00000016                 EXPORT           PendSV_Handler             [WEA
K]
  176 00000016 E7FE            B                .
  177 00000018                 ENDP
  178 00000018         SysTick_Handler
                               PROC
  179 00000018                 EXPORT           SysTick_Handler            [WEA
K]
  180 00000018 E7FE            B                .
  181 0000001A                 ENDP
  182 0000001A         
  183 0000001A         Default_Handler
                               PROC
  184 0000001A         
  185 0000001A                 EXPORT           WWDG_IRQHandler            [WEA
K]
  186 0000001A                 EXPORT           PVD_IRQHandler             [WEA
K]
  187 0000001A                 EXPORT           TAMPER_IRQHandler          [WEA
K]
  188 0000001A                 EXPORT           RTC_IRQHandler             [WEA
K]
  189 0000001A                 EXPORT           FLASH_IRQHandler           [WEA
K]
  190 0000001A                 EXPORT           RCC_IRQHandler             [WEA
K]
  191 0000001A                 EXPORT           EXTI0_IRQHandler           [WEA
K]
  192 0000001A                 EXPORT           EXTI1_IRQHandler           [WEA
K]
  193 0000001A                 EXPORT           EXTI2_IRQHandler           [WEA
K]
  194 0000001A                 EXPORT           EXTI3_IRQHandler           [WEA
K]
  195 0000001A                 EXPORT           EXTI4_IRQHandler           [WEA
K]
  196 0000001A                 EXPORT           DMA1_Channel1_IRQHandler   [WEA
K]
  197 0000001A                 EXPORT           DMA1_Channel2_IRQHandler   [WEA
K]
  198 0000001A                 EXPORT           DMA1_Channel3_IRQHandler   [WEA
K]
  199 0000001A                 EXPORT           DMA1_Channel4_IRQHandler   [WEA
K]
  200 0000001A                 EXPORT           DMA1_Channel5_IRQHandler   [WEA
K]



ARM Macro Assembler    Page 6 


  201 0000001A                 EXPORT           DMA1_Channel6_IRQHandler   [WEA
K]
  202 0000001A                 EXPORT           DMA1_Channel7_IRQHandler   [WEA
K]
  203 0000001A                 EXPORT           ADC1_2_IRQHandler          [WEA
K]
  204 0000001A                 EXPORT           USB_HP_CAN1_TX_IRQHandler  [WEA
K]
  205 0000001A                 EXPORT           USB_LP_CAN1_RX0_IRQHandler [WEA
K]
  206 0000001A                 EXPORT           CAN1_RX1_IRQHandler        [WEA
K]
  207 0000001A                 EXPORT           CAN1_SCE_IRQHandler        [WEA
K]
  208 0000001A                 EXPORT           EXTI9_5_IRQHandler         [WEA
K]
  209 0000001A                 EXPORT           TIM1_BRK_IRQHandler        [WEA
K]
  210 0000001A                 EXPORT           TIM1_UP_IRQHandler         [WEA
K]
  211 0000001A                 EXPORT           TIM1_TRG_COM_IRQHandler    [WEA
K]
  212 0000001A                 EXPORT           TIM1_CC_IRQHandler         [WEA
K]
  213 0000001A                 EXPORT           TIM2_IRQHandler            [WEA
K]
  214 0000001A                 EXPORT           TIM3_IRQHandler            [WEA
K]
  215 0000001A                 EXPORT           TIM4_IRQHandler            [WEA
K]
  216 0000001A                 EXPORT           I2C1_EV_IRQHandler         [WEA
K]
  217 0000001A                 EXPORT           I2C1_ER_IRQHandler         [WEA
K]
  218 0000001A                 EXPORT           I2C2_EV_IRQHandler         [WEA
K]
  219 0000001A                 EXPORT           I2C2_ER_IRQHandler         [WEA
K]
  220 0000001A                 EXPORT           SPI1_IRQHandler            [WEA
K]
  221 0000001A                 EXPORT           SPI2_IRQHandler            [WEA
K]
  222 0000001A                 EXPORT           USART1_IRQHandler          [WEA
K]
  223 0000001A                 EXPORT           USART2_IRQHandler          [WEA
K]
  224 0000001A                 EXPORT           USART3_IRQHandler          [WEA
K]
  225 0000001A                 EXPORT           EXTI15_10_IRQHandler       [WEA
K]
  226 0000001A                 EXPORT           RTCAlarm_IRQHandler        [WEA
K]
  227 0000001A                 EXPORT           USBWakeUp_IRQHandler       [WEA
K]
  228 0000001A         
  229 0000001A         WWDG_IRQHandler
  230 0000001A         PVD_IRQHandler
  231 0000001A         TAMPER_IRQHandler
  232 0000001A         RTC_IRQHandler



ARM Macro Assembler    Page 7 


  233 0000001A         FLASH_IRQHandler
  234 0000001A         RCC_IRQHandler
  235 0000001A         EXTI0_IRQHandler
  236 0000001A         EXTI1_IRQHandler
  237 0000001A         EXTI2_IRQHandler
  238 0000001A         EXTI3_IRQHandler
  239 0000001A         EXTI4_IRQHandler
  240 0000001A         DMA1_Channel1_IRQHandler
  241 0000001A         DMA1_Channel2_IRQHandler
  242 0000001A         DMA1_Channel3_IRQHandler
  243 0000001A         DMA1_Channel4_IRQHandler
  244 0000001A         DMA1_Channel5_IRQHandler
  245 0000001A         DMA1_Channel6_IRQHandler
  246 0000001A         DMA1_Channel7_IRQHandler
  247 0000001A         ADC1_2_IRQHandler
  248 0000001A         USB_HP_CAN1_TX_IRQHandler
  249 0000001A         USB_LP_CAN1_RX0_IRQHandler
  250 0000001A         CAN1_RX1_IRQHandler
  251 0000001A         CAN1_SCE_IRQHandler
  252 0000001A         EXTI9_5_IRQHandler
  253 0000001A         TIM1_BRK_IRQHandler
  254 0000001A         TIM1_UP_IRQHandler
  255 0000001A         TIM1_TRG_COM_IRQHandler
  256 0000001A         TIM1_CC_IRQHandler
  257 0000001A         TIM2_IRQHandler
  258 0000001A         TIM3_IRQHandler
  259 0000001A         TIM4_IRQHandler
  260 0000001A         I2C1_EV_IRQHandler
  261 0000001A         I2C1_ER_IRQHandler
  262 0000001A         I2C2_EV_IRQHandler
  263 0000001A         I2C2_ER_IRQHandler
  264 0000001A         SPI1_IRQHandler
  265 0000001A         SPI2_IRQHandler
  266 0000001A         USART1_IRQHandler
  267 0000001A         USART2_IRQHandler
  268 0000001A         USART3_IRQHandler
  269 0000001A         EXTI15_10_IRQHandler
  270 0000001A         RTCAlarm_IRQHandler
  271 0000001A         USBWakeUp_IRQHandler
  272 0000001A         
  273 0000001A E7FE            B                .
  274 0000001C         
  275 0000001C                 ENDP
  276 0000001C         
  277 0000001C                 ALIGN
  278 0000001C         
  279 0000001C         ;*******************************************************
                       ************************
  280 0000001C         ; User Stack and Heap initialization
  281 0000001C         ;*******************************************************
                       ************************
  282 0000001C                 IF               :DEF:__MICROLIB
  289 0000001C         
  290 0000001C                 IMPORT           __use_two_region_memory
  291 0000001C                 EXPORT           __user_initial_stackheap
  292 0000001C         
  293 0000001C         __user_initial_stackheap
  294 0000001C         
  295 0000001C 4804            LDR              R0, =  Heap_Mem



ARM Macro Assembler    Page 8 


  296 0000001E 4905            LDR              R1, =(Stack_Mem + Stack_Size)
  297 00000020 4A05            LDR              R2, = (Heap_Mem +  Heap_Size)
  298 00000022 4B06            LDR              R3, = Stack_Mem
  299 00000024 4770            BX               LR
  300 00000026         
  301 00000026 00 00           ALIGN
  302 00000028         
  303 00000028                 ENDIF
  304 00000028         
  305 00000028                 END
              00000000 
              00000000 
              00000000 
              00000400 
              00000200 
              00000000 
Command Line: --debug --xref --diag_suppress=9931 --cpu=Cortex-M3 --apcs=interw
ork --depend=.\objects\startup_stm32f10x_md.d -o.\objects\startup_stm32f10x_md.
o -ID:\keil5\Keil\STM32F1xx_DFP\2.4.1\Device\Include -ID:\keil5\ARM\CMSIS\Inclu
de --predefine="__UVISION_VERSION SETA 542" --predefine="STM32F10X_MD SETA 1" -
-list=.\listings\startup_stm32f10x_md.lst Start\startup_stm32f10x_md.s



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

STACK 00000000

Symbol: STACK
   Definitions
      At line 35 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: STACK unused
Stack_Mem 00000000

Symbol: Stack_Mem
   Definitions
      At line 36 in file Start\startup_stm32f10x_md.s
   Uses
      At line 296 in file Start\startup_stm32f10x_md.s
      At line 298 in file Start\startup_stm32f10x_md.s

__initial_sp 00000400

Symbol: __initial_sp
   Definitions
      At line 37 in file Start\startup_stm32f10x_md.s
   Uses
      At line 61 in file Start\startup_stm32f10x_md.s
Comment: __initial_sp used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

HEAP 00000000

Symbol: HEAP
   Definitions
      At line 46 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: HEAP unused
Heap_Mem 00000000

Symbol: Heap_Mem
   Definitions
      At line 48 in file Start\startup_stm32f10x_md.s
   Uses
      At line 295 in file Start\startup_stm32f10x_md.s
      At line 297 in file Start\startup_stm32f10x_md.s

__heap_base 00000000

Symbol: __heap_base
   Definitions
      At line 47 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: __heap_base unused
__heap_limit 00000200

Symbol: __heap_limit
   Definitions
      At line 49 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: __heap_limit unused
4 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

RESET 00000000

Symbol: RESET
   Definitions
      At line 56 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: RESET unused
__Vectors 00000000

Symbol: __Vectors
   Definitions
      At line 61 in file Start\startup_stm32f10x_md.s
   Uses
      At line 57 in file Start\startup_stm32f10x_md.s
      At line 124 in file Start\startup_stm32f10x_md.s

__Vectors_End 000000EC

Symbol: __Vectors_End
   Definitions
      At line 122 in file Start\startup_stm32f10x_md.s
   Uses
      At line 58 in file Start\startup_stm32f10x_md.s
      At line 124 in file Start\startup_stm32f10x_md.s

3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Relocatable symbols

.text 00000000

Symbol: .text
   Definitions
      At line 126 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: .text unused
ADC1_2_IRQHandler 0000001A

Symbol: ADC1_2_IRQHandler
   Definitions
      At line 247 in file Start\startup_stm32f10x_md.s
   Uses
      At line 97 in file Start\startup_stm32f10x_md.s
      At line 203 in file Start\startup_stm32f10x_md.s

BusFault_Handler 0000000E

Symbol: BusFault_Handler
   Definitions
      At line 156 in file Start\startup_stm32f10x_md.s
   Uses
      At line 66 in file Start\startup_stm32f10x_md.s
      At line 157 in file Start\startup_stm32f10x_md.s

CAN1_RX1_IRQHandler 0000001A

Symbol: CAN1_RX1_IRQHandler
   Definitions
      At line 250 in file Start\startup_stm32f10x_md.s
   Uses
      At line 100 in file Start\startup_stm32f10x_md.s
      At line 206 in file Start\startup_stm32f10x_md.s

CAN1_SCE_IRQHandler 0000001A

Symbol: CAN1_SCE_IRQHandler
   Definitions
      At line 251 in file Start\startup_stm32f10x_md.s
   Uses
      At line 101 in file Start\startup_stm32f10x_md.s
      At line 207 in file Start\startup_stm32f10x_md.s

DMA1_Channel1_IRQHandler 0000001A

Symbol: DMA1_Channel1_IRQHandler
   Definitions
      At line 240 in file Start\startup_stm32f10x_md.s
   Uses
      At line 90 in file Start\startup_stm32f10x_md.s
      At line 196 in file Start\startup_stm32f10x_md.s

DMA1_Channel2_IRQHandler 0000001A

Symbol: DMA1_Channel2_IRQHandler
   Definitions
      At line 241 in file Start\startup_stm32f10x_md.s
   Uses



ARM Macro Assembler    Page 2 Alphabetic symbol ordering
Relocatable symbols

      At line 91 in file Start\startup_stm32f10x_md.s
      At line 197 in file Start\startup_stm32f10x_md.s

DMA1_Channel3_IRQHandler 0000001A

Symbol: DMA1_Channel3_IRQHandler
   Definitions
      At line 242 in file Start\startup_stm32f10x_md.s
   Uses
      At line 92 in file Start\startup_stm32f10x_md.s
      At line 198 in file Start\startup_stm32f10x_md.s

DMA1_Channel4_IRQHandler 0000001A

Symbol: DMA1_Channel4_IRQHandler
   Definitions
      At line 243 in file Start\startup_stm32f10x_md.s
   Uses
      At line 93 in file Start\startup_stm32f10x_md.s
      At line 199 in file Start\startup_stm32f10x_md.s

DMA1_Channel5_IRQHandler 0000001A

Symbol: DMA1_Channel5_IRQHandler
   Definitions
      At line 244 in file Start\startup_stm32f10x_md.s
   Uses
      At line 94 in file Start\startup_stm32f10x_md.s
      At line 200 in file Start\startup_stm32f10x_md.s

DMA1_Channel6_IRQHandler 0000001A

Symbol: DMA1_Channel6_IRQHandler
   Definitions
      At line 245 in file Start\startup_stm32f10x_md.s
   Uses
      At line 95 in file Start\startup_stm32f10x_md.s
      At line 201 in file Start\startup_stm32f10x_md.s

DMA1_Channel7_IRQHandler 0000001A

Symbol: DMA1_Channel7_IRQHandler
   Definitions
      At line 246 in file Start\startup_stm32f10x_md.s
   Uses
      At line 96 in file Start\startup_stm32f10x_md.s
      At line 202 in file Start\startup_stm32f10x_md.s

DebugMon_Handler 00000014

Symbol: DebugMon_Handler
   Definitions
      At line 170 in file Start\startup_stm32f10x_md.s
   Uses
      At line 73 in file Start\startup_stm32f10x_md.s
      At line 171 in file Start\startup_stm32f10x_md.s

Default_Handler 0000001A




ARM Macro Assembler    Page 3 Alphabetic symbol ordering
Relocatable symbols

Symbol: Default_Handler
   Definitions
      At line 183 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: Default_Handler unused
EXTI0_IRQHandler 0000001A

Symbol: EXTI0_IRQHandler
   Definitions
      At line 235 in file Start\startup_stm32f10x_md.s
   Uses
      At line 85 in file Start\startup_stm32f10x_md.s
      At line 191 in file Start\startup_stm32f10x_md.s

EXTI15_10_IRQHandler 0000001A

Symbol: EXTI15_10_IRQHandler
   Definitions
      At line 269 in file Start\startup_stm32f10x_md.s
   Uses
      At line 119 in file Start\startup_stm32f10x_md.s
      At line 225 in file Start\startup_stm32f10x_md.s

EXTI1_IRQHandler 0000001A

Symbol: EXTI1_IRQHandler
   Definitions
      At line 236 in file Start\startup_stm32f10x_md.s
   Uses
      At line 86 in file Start\startup_stm32f10x_md.s
      At line 192 in file Start\startup_stm32f10x_md.s

EXTI2_IRQHandler 0000001A

Symbol: EXTI2_IRQHandler
   Definitions
      At line 237 in file Start\startup_stm32f10x_md.s
   Uses
      At line 87 in file Start\startup_stm32f10x_md.s
      At line 193 in file Start\startup_stm32f10x_md.s

EXTI3_IRQHandler 0000001A

Symbol: EXTI3_IRQHandler
   Definitions
      At line 238 in file Start\startup_stm32f10x_md.s
   Uses
      At line 88 in file Start\startup_stm32f10x_md.s
      At line 194 in file Start\startup_stm32f10x_md.s

EXTI4_IRQHandler 0000001A

Symbol: EXTI4_IRQHandler
   Definitions
      At line 239 in file Start\startup_stm32f10x_md.s
   Uses
      At line 89 in file Start\startup_stm32f10x_md.s
      At line 195 in file Start\startup_stm32f10x_md.s



ARM Macro Assembler    Page 4 Alphabetic symbol ordering
Relocatable symbols


EXTI9_5_IRQHandler 0000001A

Symbol: EXTI9_5_IRQHandler
   Definitions
      At line 252 in file Start\startup_stm32f10x_md.s
   Uses
      At line 102 in file Start\startup_stm32f10x_md.s
      At line 208 in file Start\startup_stm32f10x_md.s

FLASH_IRQHandler 0000001A

Symbol: FLASH_IRQHandler
   Definitions
      At line 233 in file Start\startup_stm32f10x_md.s
   Uses
      At line 83 in file Start\startup_stm32f10x_md.s
      At line 189 in file Start\startup_stm32f10x_md.s

HardFault_Handler 0000000A

Symbol: HardFault_Handler
   Definitions
      At line 146 in file Start\startup_stm32f10x_md.s
   Uses
      At line 64 in file Start\startup_stm32f10x_md.s
      At line 147 in file Start\startup_stm32f10x_md.s

I2C1_ER_IRQHandler 0000001A

Symbol: I2C1_ER_IRQHandler
   Definitions
      At line 261 in file Start\startup_stm32f10x_md.s
   Uses
      At line 111 in file Start\startup_stm32f10x_md.s
      At line 217 in file Start\startup_stm32f10x_md.s

I2C1_EV_IRQHandler 0000001A

Symbol: I2C1_EV_IRQHandler
   Definitions
      At line 260 in file Start\startup_stm32f10x_md.s
   Uses
      At line 110 in file Start\startup_stm32f10x_md.s
      At line 216 in file Start\startup_stm32f10x_md.s

I2C2_ER_IRQHandler 0000001A

Symbol: I2C2_ER_IRQHandler
   Definitions
      At line 263 in file Start\startup_stm32f10x_md.s
   Uses
      At line 113 in file Start\startup_stm32f10x_md.s
      At line 219 in file Start\startup_stm32f10x_md.s

I2C2_EV_IRQHandler 0000001A

Symbol: I2C2_EV_IRQHandler
   Definitions



ARM Macro Assembler    Page 5 Alphabetic symbol ordering
Relocatable symbols

      At line 262 in file Start\startup_stm32f10x_md.s
   Uses
      At line 112 in file Start\startup_stm32f10x_md.s
      At line 218 in file Start\startup_stm32f10x_md.s

MemManage_Handler 0000000C

Symbol: MemManage_Handler
   Definitions
      At line 151 in file Start\startup_stm32f10x_md.s
   Uses
      At line 65 in file Start\startup_stm32f10x_md.s
      At line 152 in file Start\startup_stm32f10x_md.s

NMI_Handler 00000008

Symbol: NMI_Handler
   Definitions
      At line 141 in file Start\startup_stm32f10x_md.s
   Uses
      At line 63 in file Start\startup_stm32f10x_md.s
      At line 142 in file Start\startup_stm32f10x_md.s

PVD_IRQHandler 0000001A

Symbol: PVD_IRQHandler
   Definitions
      At line 230 in file Start\startup_stm32f10x_md.s
   Uses
      At line 80 in file Start\startup_stm32f10x_md.s
      At line 186 in file Start\startup_stm32f10x_md.s

PendSV_Handler 00000016

Symbol: PendSV_Handler
   Definitions
      At line 174 in file Start\startup_stm32f10x_md.s
   Uses
      At line 75 in file Start\startup_stm32f10x_md.s
      At line 175 in file Start\startup_stm32f10x_md.s

RCC_IRQHandler 0000001A

Symbol: RCC_IRQHandler
   Definitions
      At line 234 in file Start\startup_stm32f10x_md.s
   Uses
      At line 84 in file Start\startup_stm32f10x_md.s
      At line 190 in file Start\startup_stm32f10x_md.s

RTCAlarm_IRQHandler 0000001A

Symbol: RTCAlarm_IRQHandler
   Definitions
      At line 270 in file Start\startup_stm32f10x_md.s
   Uses
      At line 120 in file Start\startup_stm32f10x_md.s
      At line 226 in file Start\startup_stm32f10x_md.s




ARM Macro Assembler    Page 6 Alphabetic symbol ordering
Relocatable symbols

RTC_IRQHandler 0000001A

Symbol: RTC_IRQHandler
   Definitions
      At line 232 in file Start\startup_stm32f10x_md.s
   Uses
      At line 82 in file Start\startup_stm32f10x_md.s
      At line 188 in file Start\startup_stm32f10x_md.s

Reset_Handler 00000000

Symbol: Reset_Handler
   Definitions
      At line 129 in file Start\startup_stm32f10x_md.s
   Uses
      At line 62 in file Start\startup_stm32f10x_md.s
      At line 130 in file Start\startup_stm32f10x_md.s

SPI1_IRQHandler 0000001A

Symbol: SPI1_IRQHandler
   Definitions
      At line 264 in file Start\startup_stm32f10x_md.s
   Uses
      At line 114 in file Start\startup_stm32f10x_md.s
      At line 220 in file Start\startup_stm32f10x_md.s

SPI2_IRQHandler 0000001A

Symbol: SPI2_IRQHandler
   Definitions
      At line 265 in file Start\startup_stm32f10x_md.s
   Uses
      At line 115 in file Start\startup_stm32f10x_md.s
      At line 221 in file Start\startup_stm32f10x_md.s

SVC_Handler 00000012

Symbol: SVC_Handler
   Definitions
      At line 165 in file Start\startup_stm32f10x_md.s
   Uses
      At line 72 in file Start\startup_stm32f10x_md.s
      At line 166 in file Start\startup_stm32f10x_md.s

SysTick_Handler 00000018

Symbol: SysTick_Handler
   Definitions
      At line 178 in file Start\startup_stm32f10x_md.s
   Uses
      At line 76 in file Start\startup_stm32f10x_md.s
      At line 179 in file Start\startup_stm32f10x_md.s

TAMPER_IRQHandler 0000001A

Symbol: TAMPER_IRQHandler
   Definitions
      At line 231 in file Start\startup_stm32f10x_md.s



ARM Macro Assembler    Page 7 Alphabetic symbol ordering
Relocatable symbols

   Uses
      At line 81 in file Start\startup_stm32f10x_md.s
      At line 187 in file Start\startup_stm32f10x_md.s

TIM1_BRK_IRQHandler 0000001A

Symbol: TIM1_BRK_IRQHandler
   Definitions
      At line 253 in file Start\startup_stm32f10x_md.s
   Uses
      At line 103 in file Start\startup_stm32f10x_md.s
      At line 209 in file Start\startup_stm32f10x_md.s

TIM1_CC_IRQHandler 0000001A

Symbol: TIM1_CC_IRQHandler
   Definitions
      At line 256 in file Start\startup_stm32f10x_md.s
   Uses
      At line 106 in file Start\startup_stm32f10x_md.s
      At line 212 in file Start\startup_stm32f10x_md.s

TIM1_TRG_COM_IRQHandler 0000001A

Symbol: TIM1_TRG_COM_IRQHandler
   Definitions
      At line 255 in file Start\startup_stm32f10x_md.s
   Uses
      At line 105 in file Start\startup_stm32f10x_md.s
      At line 211 in file Start\startup_stm32f10x_md.s

TIM1_UP_IRQHandler 0000001A

Symbol: TIM1_UP_IRQHandler
   Definitions
      At line 254 in file Start\startup_stm32f10x_md.s
   Uses
      At line 104 in file Start\startup_stm32f10x_md.s
      At line 210 in file Start\startup_stm32f10x_md.s

TIM2_IRQHandler 0000001A

Symbol: TIM2_IRQHandler
   Definitions
      At line 257 in file Start\startup_stm32f10x_md.s
   Uses
      At line 107 in file Start\startup_stm32f10x_md.s
      At line 213 in file Start\startup_stm32f10x_md.s

TIM3_IRQHandler 0000001A

Symbol: TIM3_IRQHandler
   Definitions
      At line 258 in file Start\startup_stm32f10x_md.s
   Uses
      At line 108 in file Start\startup_stm32f10x_md.s
      At line 214 in file Start\startup_stm32f10x_md.s

TIM4_IRQHandler 0000001A



ARM Macro Assembler    Page 8 Alphabetic symbol ordering
Relocatable symbols


Symbol: TIM4_IRQHandler
   Definitions
      At line 259 in file Start\startup_stm32f10x_md.s
   Uses
      At line 109 in file Start\startup_stm32f10x_md.s
      At line 215 in file Start\startup_stm32f10x_md.s

USART1_IRQHandler 0000001A

Symbol: USART1_IRQHandler
   Definitions
      At line 266 in file Start\startup_stm32f10x_md.s
   Uses
      At line 116 in file Start\startup_stm32f10x_md.s
      At line 222 in file Start\startup_stm32f10x_md.s

USART2_IRQHandler 0000001A

Symbol: USART2_IRQHandler
   Definitions
      At line 267 in file Start\startup_stm32f10x_md.s
   Uses
      At line 117 in file Start\startup_stm32f10x_md.s
      At line 223 in file Start\startup_stm32f10x_md.s

USART3_IRQHandler 0000001A

Symbol: USART3_IRQHandler
   Definitions
      At line 268 in file Start\startup_stm32f10x_md.s
   Uses
      At line 118 in file Start\startup_stm32f10x_md.s
      At line 224 in file Start\startup_stm32f10x_md.s

USBWakeUp_IRQHandler 0000001A

Symbol: USBWakeUp_IRQHandler
   Definitions
      At line 271 in file Start\startup_stm32f10x_md.s
   Uses
      At line 121 in file Start\startup_stm32f10x_md.s
      At line 227 in file Start\startup_stm32f10x_md.s

USB_HP_CAN1_TX_IRQHandler 0000001A

Symbol: USB_HP_CAN1_TX_IRQHandler
   Definitions
      At line 248 in file Start\startup_stm32f10x_md.s
   Uses
      At line 98 in file Start\startup_stm32f10x_md.s
      At line 204 in file Start\startup_stm32f10x_md.s

USB_LP_CAN1_RX0_IRQHandler 0000001A

Symbol: USB_LP_CAN1_RX0_IRQHandler
   Definitions
      At line 249 in file Start\startup_stm32f10x_md.s
   Uses



ARM Macro Assembler    Page 9 Alphabetic symbol ordering
Relocatable symbols

      At line 99 in file Start\startup_stm32f10x_md.s
      At line 205 in file Start\startup_stm32f10x_md.s

UsageFault_Handler 00000010

Symbol: UsageFault_Handler
   Definitions
      At line 161 in file Start\startup_stm32f10x_md.s
   Uses
      At line 67 in file Start\startup_stm32f10x_md.s
      At line 162 in file Start\startup_stm32f10x_md.s

WWDG_IRQHandler 0000001A

Symbol: WWDG_IRQHandler
   Definitions
      At line 229 in file Start\startup_stm32f10x_md.s
   Uses
      At line 79 in file Start\startup_stm32f10x_md.s
      At line 185 in file Start\startup_stm32f10x_md.s

__user_initial_stackheap 0000001C

Symbol: __user_initial_stackheap
   Definitions
      At line 293 in file Start\startup_stm32f10x_md.s
   Uses
      At line 291 in file Start\startup_stm32f10x_md.s
Comment: __user_initial_stackheap used once
56 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
Absolute symbols

Heap_Size 00000200

Symbol: Heap_Size
   Definitions
      At line 44 in file Start\startup_stm32f10x_md.s
   Uses
      At line 48 in file Start\startup_stm32f10x_md.s
      At line 297 in file Start\startup_stm32f10x_md.s

Stack_Size 00000400

Symbol: Stack_Size
   Definitions
      At line 33 in file Start\startup_stm32f10x_md.s
   Uses
      At line 36 in file Start\startup_stm32f10x_md.s
      At line 296 in file Start\startup_stm32f10x_md.s

__Vectors_Size 000000EC

Symbol: __Vectors_Size
   Definitions
      At line 124 in file Start\startup_stm32f10x_md.s
   Uses
      At line 59 in file Start\startup_stm32f10x_md.s
Comment: __Vectors_Size used once
3 symbols



ARM Macro Assembler    Page 1 Alphabetic symbol ordering
External symbols

SystemInit 00000000

Symbol: SystemInit
   Definitions
      At line 132 in file Start\startup_stm32f10x_md.s
   Uses
      At line 133 in file Start\startup_stm32f10x_md.s
Comment: SystemInit used once
__main 00000000

Symbol: __main
   Definitions
      At line 131 in file Start\startup_stm32f10x_md.s
   Uses
      At line 135 in file Start\startup_stm32f10x_md.s
Comment: __main used once
__use_two_region_memory 00000000

Symbol: __use_two_region_memory
   Definitions
      At line 290 in file Start\startup_stm32f10x_md.s
   Uses
      None
Comment: __use_two_region_memory unused
3 symbols
408 symbols in table
