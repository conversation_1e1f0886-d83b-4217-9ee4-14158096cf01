.\build\motor.o: ..\..\CODE\motor.c
.\build\motor.o: ..\..\CODE\inc\motor.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/driverlib.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/adc14.h
.\build\motor.o: E:\keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\build\motor.o: E:\keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/inc/msp.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/inc/msp432p401r.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/inc/msp_compatibility.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/inc/msp432p401r_classic.h
.\build\motor.o: ..\..\msp432p4xx_lib\CMSIS\Include\core_cm4.h
.\build\motor.o: ..\..\msp432p4xx_lib\CMSIS\Include\cmsis_version.h
.\build\motor.o: ..\..\msp432p4xx_lib\CMSIS\Include\cmsis_compiler.h
.\build\motor.o: ..\..\msp432p4xx_lib\CMSIS\Include\cmsis_armcc.h
.\build\motor.o: ..\..\msp432p4xx_lib\CMSIS\Include\mpu_armv7.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/inc/system_msp432p401r.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/aes256.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/comp_e.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/cpu.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/crc32.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/cs.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/dma.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/interrupt.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/eusci.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/fpu.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/gpio.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/i2c.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/mpu.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/pcm.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/pmap.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/pss.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/ref_a.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/reset.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/rom.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/rom_map.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/rtc_c.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/spi.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/systick.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/timer32.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/timer_a.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/uart.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/wdt_a.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/sysctl.h
.\build\motor.o: ..\..\msp432p4xx_lib\ti/devices/msp432p4xx/driverlib/flash.h
.\build\motor.o: ..\..\CODE\inc\globalvar.h
.\build\motor.o: ..\..\CODE\inc\PIDcontrol.h
.\build\motor.o: ..\..\sys\inc\sysinit.h
.\build\motor.o: ..\..\sys\inc\usart.h
.\build\motor.o: E:\keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\build\motor.o: ..\..\sys\inc\delay.h
.\build\motor.o: ..\..\hardware\inc\led.h
.\build\motor.o: ..\..\CODE\inc\globalvar.h
.\build\motor.o: ..\..\hardware\inc\key.h
.\build\motor.o: ..\..\hardware\inc\tim32.h
.\build\motor.o: ..\..\hardware\inc\timA.h
.\build\motor.o: ..\..\hardware\inc\oled.h
.\build\motor.o: ..\..\CODE\inc\encoder.h
.\build\motor.o: ..\..\CODE\inc\motor.h
.\build\motor.o: ..\..\CODE\inc\grayscale.h
.\build\motor.o: ..\..\CODE\inc\FSM.h
