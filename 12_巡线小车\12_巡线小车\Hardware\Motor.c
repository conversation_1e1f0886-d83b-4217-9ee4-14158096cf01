#include "stm32f10x.h"                  // Device header
#include "Motor.h"

/*
1.���ļ�����Ҫ���κθ���
*/


void Motor_Init(void)
{
	// 时钟初始化
	RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOB, ENABLE);

	// PWM输出GPIO初始化 - 只保留左前轮(PA0)和右前轮(PA2)
	GPIO_InitTypeDef GPIO_InitStructure;
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_2;  // 只初始化PA0和PA2
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOA, &GPIO_InitStructure);

	// 方向控制GPIO初始化 - 只保留左前轮(PB0/PB1)和右前轮(PB12/PB13)
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_12 | GPIO_Pin_13;
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
	GPIO_Init(GPIOB, &GPIO_InitStructure);

	// 设置初始方向 - 正转方向
	GPIO_SetBits(GPIOB, GPIO_Pin_0);    // 左前轮正转
	GPIO_ResetBits(GPIOB, GPIO_Pin_1);
	GPIO_SetBits(GPIOB, GPIO_Pin_12);   // 右前轮正转
	GPIO_ResetBits(GPIOB, GPIO_Pin_13);
	// TIM2 PWM初始化 - 只配置CH1(左前轮)和CH3(右前轮)
	TIM_InternalClockConfig(TIM2);

	TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
	TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;
	TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up;
	TIM_TimeBaseInitStructure.TIM_Period = 100 - 1;		// ARR
	TIM_TimeBaseInitStructure.TIM_Prescaler = 36 - 1;		// PSC
	TIM_TimeBaseInitStructure.TIM_RepetitionCounter = 0;
	TIM_TimeBaseInit(TIM2, &TIM_TimeBaseInitStructure);

	TIM_OCInitTypeDef TIM_OCInitStructure;
	TIM_OCStructInit(&TIM_OCInitStructure);

	// 配置CH1 - 左前轮PWM
	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
	TIM_OCInitStructure.TIM_Pulse = 0;		// CCR
	TIM_OC1Init(TIM2, &TIM_OCInitStructure);

	// 配置CH3 - 右前轮PWM
	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1;
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High;
	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable;
	TIM_OCInitStructure.TIM_Pulse = 0;		// CCR
	TIM_OC3Init(TIM2, &TIM_OCInitStructure);

	TIM_Cmd(TIM2, ENABLE);
}


// 左前轮速度控制
void LeftWheelFront_Speed(int Speed)
{
	if(Speed > 0)
	{
		GPIO_SetBits(GPIOB, GPIO_Pin_0);     // 正转
		GPIO_ResetBits(GPIOB, GPIO_Pin_1);
		TIM_SetCompare1(TIM2, Speed);
	}
	else if(Speed < 0)
	{
		GPIO_SetBits(GPIOB, GPIO_Pin_1);     // 反转
		GPIO_ResetBits(GPIOB, GPIO_Pin_0);
		TIM_SetCompare1(TIM2, -Speed);
	}
	else
	{
		TIM_SetCompare1(TIM2, 0);            // 停止
	}
}
// 右前轮速度控制
void RightWheelFront_Speed(int Speed)
{
	if(Speed > 0)
	{
		GPIO_SetBits(GPIOB, GPIO_Pin_12);    // 正转
		GPIO_ResetBits(GPIOB, GPIO_Pin_13);
		TIM_SetCompare3(TIM2, Speed);
	}
	else if(Speed < 0)
	{
		GPIO_SetBits(GPIOB, GPIO_Pin_13);    // 反转
		GPIO_ResetBits(GPIOB, GPIO_Pin_12);
		TIM_SetCompare3(TIM2, -Speed);
	}
	else
	{
		TIM_SetCompare3(TIM2, 0);            // 停止
	}
}


