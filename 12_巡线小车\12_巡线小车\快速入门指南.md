# 🚀 STM32双轮小车快速入门指南

## 📦 开箱即用

### 1️⃣ 准备工作 (5分钟)
```
硬件清单：
✅ STM32F103C8T6开发板
✅ 两个直流电机 
✅ 电机驱动模块
✅ 两个按键开关
✅ 杜邦线若干
✅ 5V电源适配器
✅ ST-Link调试器
```

### 2️⃣ 快速接线 (10分钟)
```
电机连接：
左前轮电机 → PA0(PWM) + PB0/PB1(方向) + 5V/GND
右前轮电机 → PA2(PWM) + PB12/PB13(方向) + 5V/GND

按键连接：
Key1 → PA4 ↔ GND (控制左前轮)
Key2 → PA5 ↔ GND (控制右前轮)
```

### 3️⃣ 程序下载 (3分钟)
1. 打开Keil工程 `Project.uvprojx`
2. 连接ST-Link到STM32
3. 点击下载按钮 📥
4. 等待"Programming Done"提示

### 4️⃣ 立即测试 (2分钟)
- 按住PA4 → 左前轮转动 🔄
- 按住PA5 → 右前轮转动 🔄  
- 同时按住 → 小车前进 ⬆️
- 松开按键 → 立即停止 ⏹️

## 🎯 核心功能

| 按键操作 | 小车动作 | 应用场景 |
|----------|----------|----------|
| 按PA4 | 左转 | 向右调整方向 |
| 按PA5 | 右转 | 向左调整方向 |
| 按PA4+PA5 | 前进 | 直线行驶 |
| 无按键 | 停止 | 安全制动 |

## ⚡ 5分钟上手

### 第1分钟：连接硬件
```
STM32 → 电机驱动 → 电机
STM32 → 按键开关
电源 → STM32 + 电机驱动
```

### 第2分钟：下载程序
```
Keil → 编译 → 下载 → 完成
```

### 第3分钟：功能测试
```
按键测试 → 电机响应 → 运动控制
```

### 第4分钟：调试优化
```
检查接线 → 调整参数 → 验证功能
```

### 第5分钟：扩展思考
```
了解原理 → 规划改进 → 功能扩展
```

## 🔧 常见问题秒解

### ❓ 电机不转？
**解决方案：**
1. 检查5V电源是否接好
2. 确认电机驱动模块正常
3. 测量PA0/PA2是否有PWM输出

### ❓ 按键无效？
**解决方案：**
1. 确认按键接线：一端接PA4/PA5，另一端接GND
2. 检查按键是否损坏
3. 用万用表测试按键通断

### ❓ 程序下载失败？
**解决方案：**
1. 检查ST-Link连接线
2. 确认STM32供电正常
3. 尝试重新连接设备

### ❓ 转向相反？
**解决方案：**
1. 调换电机正负极接线
2. 或修改代码中的方向控制逻辑

## 🎮 进阶玩法

### 🔥 速度调节版
```c
// 添加速度档位切换
int speed_level = 1;  // 1-低速, 2-中速, 3-高速
int speeds[] = {40, 70, 100};

void ControlMotor() {
    int current_speed = speeds[speed_level - 1];
    // 使用current_speed控制电机
}
```

### 🔥 方向控制版
```c
// 添加正反转控制
int direction = 1;  // 1-正转, -1-反转

void ControlMotor() {
    if (key_pressed) {
        LeftWheelFront_Speed(80 * direction);
        RightWheelFront_Speed(80 * direction);
    }
}
```

### 🔥 组合动作版
```c
// 预设运动模式
void MoveForward()  { LeftWheelFront_Speed(80); RightWheelFront_Speed(80); }
void TurnLeft()     { LeftWheelFront_Speed(40); RightWheelFront_Speed(80); }
void TurnRight()    { LeftWheelFront_Speed(80); RightWheelFront_Speed(40); }
void MoveBackward() { LeftWheelFront_Speed(-80); RightWheelFront_Speed(-80); }
```

## 📈 学习路径

### 🎯 初级目标 (第1天)
- [x] 成功下载程序
- [x] 理解按键控制原理  
- [x] 掌握基本操作方法
- [x] 完成功能测试

### 🎯 中级目标 (第1周)
- [ ] 理解PWM控制原理
- [ ] 学会调整电机速度
- [ ] 掌握代码修改方法
- [ ] 实现自定义功能

### 🎯 高级目标 (第1月)
- [ ] 添加传感器模块
- [ ] 实现自动控制算法
- [ ] 集成无线通信功能
- [ ] 开发上位机软件

## 🛡️ 安全提醒

### ⚠️ 使用注意事项
- 首次测试时请将小车架空
- 确保电源电压在安全范围内
- 长时间运行注意散热
- 断电后再进行硬件修改

### 🔒 保护措施
- 代码中已加入安全停止机制
- 松开按键立即断电停止
- PWM占空比限制在安全范围
- 建议添加过流保护电路

## 📞 获取帮助

### 🆘 遇到问题？
1. **查看文档**：详细说明文档.md
2. **检查接线**：对照接线图仔细检查
3. **测试硬件**：用万用表测试各关键点
4. **重新下载**：尝试重新编译下载程序

### 💡 改进建议
- 欢迎提出功能改进建议
- 分享您的创意扩展方案
- 报告发现的问题和解决方法

---

**🎉 恭喜！您已经掌握了STM32双轮小车的基本操作！**

**下一步：** 查看详细的《使用说明文档.md》了解更多技术细节和扩展功能。
