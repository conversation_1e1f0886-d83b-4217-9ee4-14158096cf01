#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "Motor.h"
#include "Key.h"
/*
* 简化版小车控制程序
* 功能：按键控制左前轮和右前轮
* Key1(PA4) - 控制左前轮
* Key2(PA5) - 控制右前轮
*/

int main(void)
{
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);        // 设置中断优先级为2
	Motor_Init();                                          // 电机初始化
	Key_Init();                                           // 按键初始化

	while (1)
	{
		Key_GetNum();                                     // 获取按键状态

		// Key1控制左前轮
		if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_4) == 0)
		{
			LeftWheelFront_Speed(80);                     // 左前轮正转
		}
		else
		{
			LeftWheelFront_Speed(0);                      // 左前轮停止
		}

		// Key2控制右前轮
		if (GPIO_ReadInputDataBit(GPIOA, GPIO_Pin_5) == 0)
		{
			RightWheelFront_Speed(80);                    // 右前轮正转
		}
		else
		{
			RightWheelFront_Speed(0);                     // 右前轮停止
		}

		Delay_ms(10);                                     // 延时防抖
	}
}

